import React from 'react';
import { 
  CogIcon, 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from './LoadingSpinner';
import useProcessingStatus from '../hooks/useProcessingStatus';

interface ProcessingStatusIndicatorProps {
  videoId: number;
  showProgress?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const ProcessingStatusIndicator: React.FC<ProcessingStatusIndicatorProps> = ({
  videoId,
  showProgress = true,
  size = 'md',
  className = '',
}) => {
  const { data: status, isLoading, error } = useProcessingStatus(videoId, videoId > 0);

  const getStatusIcon = () => {
    const iconSize = size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-6 w-6' : 'h-5 w-5';
    
    if (isLoading || !status) {
      return <LoadingSpinner size={size} />;
    }

    switch (status.processing_status) {
      case 'pending':
        return <ClockIcon className={`${iconSize} text-gray-500`} />;
      case 'processing':
        return <CogIcon className={`${iconSize} text-blue-500 animate-spin`} />;
      case 'completed':
        return <CheckCircleIcon className={`${iconSize} text-green-500`} />;
      case 'failed':
        return <ExclamationTriangleIcon className={`${iconSize} text-red-500`} />;
      default:
        return <ClockIcon className={`${iconSize} text-gray-500`} />;
    }
  };

  const getStatusText = () => {
    if (isLoading || !status) {
      return 'Checking status...';
    }

    switch (status.processing_status) {
      case 'pending':
        return 'Queued for processing';
      case 'processing':
        return showProgress ? `Processing... ${status.processing_progress}%` : 'Processing...';
      case 'completed':
        return 'Processing completed';
      case 'failed':
        return 'Processing failed';
      default:
        return 'Unknown status';
    }
  };

  const getProgressColor = () => {
    if (!status) return 'bg-gray-300';
    
    switch (status.processing_status) {
      case 'processing':
        return 'bg-blue-600';
      case 'completed':
        return 'bg-green-600';
      case 'failed':
        return 'bg-red-600';
      default:
        return 'bg-gray-300';
    }
  };

  const progress = status?.processing_progress || 0;
  const isProcessing = status?.processing_status === 'processing';

  if (error) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
        <span className="text-sm text-red-600">Status unavailable</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {getStatusIcon()}
      <div className="flex-1">
        <div className={`text-${size === 'sm' ? 'xs' : 'sm'} text-gray-700 dark:text-gray-300`}>
          {getStatusText()}
        </div>
        {showProgress && isProcessing && (
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1">
            <div
              className={`h-1.5 rounded-full transition-all duration-300 ${getProgressColor()}`}
              style={{ width: `${Math.max(progress, 5)}%` }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ProcessingStatusIndicator;
