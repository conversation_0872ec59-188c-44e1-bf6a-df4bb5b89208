import React from 'react';
import { 
  CloudArrowDownIcon, 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from './LoadingSpinner';
import useDownloadStatus from '../hooks/useDownloadStatus';

interface DownloadProgressProps {
  videoId: number;
  url: string;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

const DownloadProgress: React.FC<DownloadProgressProps> = ({
  videoId,
  url,
  onComplete,
  onError,
}) => {
  const { data: status, isLoading, error } = useDownloadStatus(videoId, videoId > 0);

  // Handle completion
  React.useEffect(() => {
    if (status?.download_status === 'completed' && onComplete) {
      onComplete();
    }
    if (status?.download_status === 'failed' && onError && status.download_error) {
      onError(status.download_error);
    }
  }, [status, onComplete, onError]);

  const getStatusIcon = () => {
    if (isLoading || !status) {
      return <LoadingSpinner size="sm" />;
    }

    switch (status.download_status) {
      case 'downloading':
        return <CloudArrowDownIcon className="h-5 w-5 text-blue-500 animate-pulse" />;
      case 'completed':
        if (status.processing_status === 'completed') {
          return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
        } else {
          return <CogIcon className="h-5 w-5 text-yellow-500 animate-spin" />;
        }
      case 'failed':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <LoadingSpinner size="sm" />;
    }
  };

  const getStatusText = () => {
    if (isLoading || !status) {
      return 'Checking status...';
    }

    switch (status.download_status) {
      case 'downloading':
        return `Downloading... ${status.download_progress}%`;
      case 'completed':
        if (status.processing_status === 'processing') {
          return `Processing... ${status.processing_progress}%`;
        } else if (status.processing_status === 'completed') {
          return 'Completed';
        } else {
          return 'Download completed, queued for processing';
        }
      case 'failed':
        return `Failed: ${status.download_error || 'Unknown error'}`;
      default:
        return 'Unknown status';
    }
  };

  const getProgressValue = () => {
    if (!status) return 0;
    
    if (status.download_status === 'downloading') {
      return status.download_progress;
    } else if (status.download_status === 'completed' && status.processing_status === 'processing') {
      return status.processing_progress;
    } else if (status.download_status === 'completed' && status.processing_status === 'completed') {
      return 100;
    }
    
    return 0;
  };

  const getProgressColor = () => {
    if (!status) return 'bg-gray-300';
    
    if (status.download_status === 'downloading') {
      return 'bg-blue-600';
    } else if (status.download_status === 'completed') {
      if (status.processing_status === 'processing') {
        return 'bg-yellow-600';
      } else if (status.processing_status === 'completed') {
        return 'bg-green-600';
      }
    } else if (status.download_status === 'failed') {
      return 'bg-red-600';
    }
    
    return 'bg-gray-300';
  };

  const progress = getProgressValue();
  const isCompleted = status?.download_status === 'completed' && status?.processing_status === 'completed';
  const isFailed = status?.download_status === 'failed';

  return (
    <div className="flex items-center space-x-3 p-4 bg-white rounded-lg border">
      {getStatusIcon()}
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <p className="text-sm font-medium text-gray-900 truncate">
            {new URL(url).hostname}
          </p>
          <span className="text-xs text-gray-500">
            {progress}%
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2 mb-1">
          <div
            className={`h-2 rounded-full transition-all duration-300 ease-out ${getProgressColor()}`}
            style={{ width: `${progress}%` }}
          />
        </div>
        
        <p className="text-xs text-gray-600">
          {getStatusText()}
        </p>
        
        {isFailed && status?.download_error && (
          <p className="text-xs text-red-600 mt-1">
            {status.download_error}
          </p>
        )}
      </div>
      
      {isCompleted && (
        <div className="text-xs text-green-600 font-medium">
          Ready to view
        </div>
      )}
    </div>
  );
};

export default DownloadProgress;
