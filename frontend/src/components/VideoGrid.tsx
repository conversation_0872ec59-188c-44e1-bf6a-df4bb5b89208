import React, { useEffect } from 'react';
import { Video } from '../types';
import VideoCard from './VideoCard';
import LoadingSpinner from './LoadingSpinner';
import { useVideoSelection } from '../contexts/VideoSelectionContext';

interface VideoGridProps {
  videos: Video[];
  onLoadMore?: () => void;
  hasMore?: boolean;
  loading?: boolean;
}

const VideoGrid: React.FC<VideoGridProps> = ({
  videos,
  onLoadMore,
  hasMore = false,
  loading = false,
}) => {
  const { selectAllVideos } = useVideoSelection();

  // Listen for select all events from the bulk actions toolbar
  useEffect(() => {
    const handleSelectAll = () => {
      const videoIds = videos.map(video => video.id);
      selectAllVideos(videoIds);
    };

    window.addEventListener('selectAllVideos', handleSelectAll);
    return () => window.removeEventListener('selectAllVideos', handleSelectAll);
  }, [videos, selectAllVideos]);
  return (
    <div className="p-6">
      {/* Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {videos.map((video) => (
          <VideoCard key={video.id} video={video} />
        ))}
      </div>

      {/* Loading indicator for infinite scroll */}
      {loading && videos.length > 0 && (
        <div className="mt-8 text-center">
          <LoadingSpinner size="md" />
          <p className="text-sm text-gray-500 mt-2">Loading more videos...</p>
        </div>
      )}

      {/* End of results indicator */}
      {!hasMore && videos.length > 0 && (
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">You've reached the end of the videos</p>
        </div>
      )}
    </div>
  );
};

export default VideoGrid;
