import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { CloudArrowUpIcon, DocumentIcon } from '@heroicons/react/24/outline';

interface FileDropzoneProps {
  onFilesSelected: (files: FileList) => void;
  accept?: string;
  multiple?: boolean;
  disabled?: boolean;
  maxSize?: number; // in bytes
  className?: string;
}

const FileDropzone: React.FC<FileDropzoneProps> = ({
  onFilesSelected,
  accept = 'video/*',
  multiple = true,
  disabled = false,
  maxSize = 1024 * 1024 * 1024, // 1GB default
  className = '',
}) => {
  const [dragActive, setDragActive] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      // Create FileList from accepted files
      const dt = new DataTransfer();
      acceptedFiles.forEach(file => dt.items.add(file));
      onFilesSelected(dt.files);
    }
  }, [onFilesSelected]);

  const {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragReject,
    fileRejections,
  } = useDropzone({
    onDrop,
    accept: {
      'video/*': ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.flv', '.wmv'],
    },
    multiple,
    disabled,
    maxSize,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
  });

  const formatFileSize = (bytes: number): string => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(0)}MB`;
  };

  return (
    <div className={className}>
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragActive && !isDragReject 
            ? 'border-primary-400 bg-primary-50' 
            : isDragReject 
            ? 'border-red-400 bg-red-50'
            : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        <div className="space-y-4">
          <div className="mx-auto h-12 w-12 text-gray-400">
            {isDragActive ? (
              <CloudArrowUpIcon className="h-full w-full" />
            ) : (
              <DocumentIcon className="h-full w-full" />
            )}
          </div>
          
          <div>
            <p className="text-lg font-medium text-gray-900">
              {isDragActive 
                ? 'Drop videos here...' 
                : 'Upload video files'
              }
            </p>
            <p className="text-sm text-gray-500 mt-1">
              Drag and drop your videos here, or click to browse
            </p>
            <p className="text-xs text-gray-400 mt-2">
              Supports MP4, MOV, AVI, MKV, WebM and other video formats
              <br />
              Maximum file size: {formatFileSize(maxSize)}
            </p>
          </div>
          
          {!disabled && (
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <CloudArrowUpIcon className="h-4 w-4 mr-2" />
              Choose Files
            </button>
          )}
        </div>
      </div>

      {/* File Rejection Errors */}
      {fileRejections.length > 0 && (
        <div className="mt-4 p-4 bg-red-50 rounded-md">
          <h4 className="text-sm font-medium text-red-800 mb-2">
            Some files were rejected:
          </h4>
          <ul className="text-sm text-red-700 space-y-1">
            {fileRejections.map(({ file, errors }) => (
              <li key={file.name}>
                <strong>{file.name}</strong>:
                <ul className="ml-4 list-disc">
                  {errors.map(error => (
                    <li key={error.code}>
                      {error.code === 'file-too-large' 
                        ? `File is too large (${formatFileSize(file.size)}). Maximum size is ${formatFileSize(maxSize)}.`
                        : error.code === 'file-invalid-type'
                        ? 'Invalid file type. Please upload video files only.'
                        : error.message
                      }
                    </li>
                  ))}
                </ul>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default FileDropzone;
