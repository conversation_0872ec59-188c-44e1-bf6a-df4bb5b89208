import React from 'react';

interface DurationDistributionChartProps {
  data: Record<string, number>;
  className?: string;
}

const DurationDistributionChart: React.FC<DurationDistributionChartProps> = ({ 
  data, 
  className = '' 
}) => {
  const entries = Object.entries(data);
  
  if (entries.length === 0) {
    return (
      <div className={`text-center py-8 text-gray-500 ${className}`}>
        No duration data available
      </div>
    );
  }

  const total = entries.reduce((sum, [, count]) => sum + count, 0);
  const maxCount = Math.max(...entries.map(([, count]) => count));

  const colors = [
    'bg-blue-500',
    'bg-green-500',
    'bg-yellow-500',
    'bg-purple-500',
    'bg-pink-500',
    'bg-indigo-500',
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Bar Chart */}
      <div className="space-y-3">
        {entries.map(([duration, count], index) => {
          const percentage = total > 0 ? (count / total) * 100 : 0;
          const barWidth = maxCount > 0 ? (count / maxCount) * 100 : 0;
          const colorClass = colors[index % colors.length];
          
          return (
            <div key={duration} className="flex items-center space-x-3">
              <div className="w-16 text-sm font-medium text-gray-700 text-right">
                {duration}
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                    <div
                      className={`h-6 rounded-full ${colorClass} transition-all duration-300 flex items-center justify-end pr-2`}
                      style={{ width: `${barWidth}%` }}
                    >
                      {count > 0 && (
                        <span className="text-white text-xs font-medium">
                          {count}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="w-12 text-sm text-gray-500 text-right">
                    {percentage.toFixed(0)}%
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary */}
      <div className="border-t border-gray-200 pt-4">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Total Videos:</span>
            <span className="ml-2 font-medium">{total}</span>
          </div>
          <div>
            <span className="text-gray-500">Most Common:</span>
            <span className="ml-2 font-medium">
              {entries.length > 0 
                ? entries.reduce((max, current) => current[1] > max[1] ? current : max)[0]
                : 'N/A'
              }
            </span>
          </div>
          <div>
            <span className="text-gray-500">Categories:</span>
            <span className="ml-2 font-medium">{entries.length}</span>
          </div>
        </div>
      </div>

      {/* Legend */}
      <div className="flex flex-wrap gap-3 text-xs">
        {entries.map(([duration, count], index) => {
          const colorClass = colors[index % colors.length];
          return (
            <div key={duration} className="flex items-center space-x-1">
              <div className={`w-3 h-3 rounded ${colorClass}`} />
              <span className="text-gray-600">{duration}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default DurationDistributionChart;
