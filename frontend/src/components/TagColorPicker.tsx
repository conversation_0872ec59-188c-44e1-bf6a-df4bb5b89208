import React, { useState } from 'react';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

interface TagColorPickerProps {
  color: string;
  onChange: (color: string) => void;
  className?: string;
}

const TagColorPicker: React.FC<TagColorPickerProps> = ({ 
  color, 
  onChange, 
  className = '' 
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const predefinedColors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
    '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
    '#A3E4D7', '#F9E79F', '#D5A6BD', '#AED6F1', '#A9DFBF',
    '#FF5722', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5',
    '#2196F3', '#03A9F4', '#00BCD4', '#009688', '#4CAF50',
    '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800',
  ];

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
      >
        <div
          className="w-4 h-4 rounded border border-gray-300"
          style={{ backgroundColor: color }}
        />
        <span className="flex-1 text-left">{color}</span>
        <ChevronDownIcon className="h-4 w-4 text-gray-400" />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Color picker dropdown */}
          <div className="absolute z-20 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg">
            <div className="p-3">
              {/* Custom color input */}
              <div className="mb-3">
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Custom Color
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="color"
                    value={color}
                    onChange={(e) => onChange(e.target.value)}
                    className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
                  />
                  <input
                    type="text"
                    value={color}
                    onChange={(e) => onChange(e.target.value)}
                    className="flex-1 text-xs border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="#000000"
                  />
                </div>
              </div>

              {/* Predefined colors */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-2">
                  Predefined Colors
                </label>
                <div className="grid grid-cols-7 gap-1">
                  {predefinedColors.map((presetColor) => (
                    <button
                      key={presetColor}
                      onClick={() => {
                        onChange(presetColor);
                        setIsOpen(false);
                      }}
                      className={`w-6 h-6 rounded border-2 hover:scale-110 transition-transform ${
                        color === presetColor ? 'border-gray-800' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: presetColor }}
                      title={presetColor}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default TagColorPicker;
