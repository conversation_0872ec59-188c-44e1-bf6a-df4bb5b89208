import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  XMarkIcon,
  TagIcon,
  PlusIcon,
  CheckIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { Tag } from '../types';
import { tagApi } from '../utils/api';

interface BulkTagModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddTags: (tagIds: number[]) => void;
  onRemoveTags: (tagIds: number[]) => void;
  mode: 'add' | 'remove';
  selectedVideoCount: number;
}

const BulkTagModal: React.FC<BulkTagModalProps> = ({
  isOpen,
  onClose,
  onAddTags,
  onRemoveTags,
  mode,
  selectedVideoCount
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<Set<number>>(new Set());

  // Fetch all tags
  const { data: allTags = [], isLoading } = useQuery<Tag[]>({
    queryKey: ['tags'],
    queryFn: () => tagApi.getTags({ skip: 0, limit: 200 }),
    enabled: isOpen,
  });

  // Filter tags based on search query
  const filteredTags = allTags.filter(tag =>
    tag.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (tag.description && tag.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedTags(new Set());
      setSearchQuery('');
    }
  }, [isOpen]);

  const handleTagToggle = (tagId: number) => {
    setSelectedTags(prev => {
      const newSet = new Set(prev);
      if (newSet.has(tagId)) {
        newSet.delete(tagId);
      } else {
        newSet.add(tagId);
      }
      return newSet;
    });
  };

  const handleSubmit = () => {
    const tagIds = Array.from(selectedTags);
    if (tagIds.length === 0) return;

    if (mode === 'add') {
      onAddTags(tagIds);
    } else {
      onRemoveTags(tagIds);
    }
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* Header */}
          <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <TagIcon className="h-6 w-6 text-primary-600 dark:text-primary-400 mr-2" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {mode === 'add' ? 'Add Tags' : 'Remove Tags'}
                </h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              {mode === 'add' 
                ? `Add tags to ${selectedVideoCount} selected video${selectedVideoCount !== 1 ? 's' : ''}`
                : `Remove tags from ${selectedVideoCount} selected video${selectedVideoCount !== 1 ? 's' : ''}`
              }
            </p>

            {/* Search */}
            <div className="relative mb-4">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search tags..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Tags List */}
            <div className="max-h-64 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  Loading tags...
                </div>
              ) : filteredTags.length === 0 ? (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  {searchQuery ? 'No tags found matching your search' : 'No tags available'}
                </div>
              ) : (
                <div className="p-2 space-y-1">
                  {filteredTags.map((tag) => (
                    <div
                      key={tag.id}
                      onClick={() => handleTagToggle(tag.id)}
                      className={`
                        flex items-center p-2 rounded cursor-pointer transition-colors
                        ${selectedTags.has(tag.id)
                          ? 'bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700'
                          : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                        }
                      `}
                    >
                      <div className="flex-1 flex items-center">
                        <div
                          className="w-4 h-4 rounded-full mr-3"
                          style={{ backgroundColor: tag.color }}
                        />
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {tag.name}
                          </div>
                          {tag.description && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {tag.description}
                            </div>
                          )}
                        </div>
                      </div>
                      {selectedTags.has(tag.id) && (
                        <CheckIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={handleSubmit}
              disabled={selectedTags.size === 0}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {mode === 'add' ? 'Add Tags' : 'Remove Tags'} ({selectedTags.size})
            </button>
            <button
              onClick={onClose}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkTagModal;
