import React from 'react';
import { ExclamationTriangleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface ServerOverloadMessageProps {
  onRetry?: () => void;
  message?: string;
}

const ServerOverloadMessage: React.FC<ServerOverloadMessageProps> = ({
  onRetry,
  message = "The server is temporarily overloaded due to video processing. Please wait a moment and try again."
}) => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
        <div className="flex justify-center mb-4">
          <ExclamationTriangleIcon className="h-16 w-16 text-yellow-500" />
        </div>
        
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Server Temporarily Overloaded
        </h2>
        
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          {message}
        </p>
        
        <div className="space-y-3">
          {onRetry && (
            <button
              onClick={onRetry}
              className="w-full flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              <ArrowPathIcon className="h-4 w-4" />
              Try Again
            </button>
          )}
          
          <div className="text-sm text-gray-500 dark:text-gray-400">
            <p>This usually happens when:</p>
            <ul className="mt-2 text-left space-y-1">
              <li>• Videos are being processed with AI</li>
              <li>• Multiple downloads are running</li>
              <li>• The server is under heavy load</li>
            </ul>
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              💡 <strong>Tip:</strong> Try refreshing the page in a few minutes when processing completes.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServerOverloadMessage;
