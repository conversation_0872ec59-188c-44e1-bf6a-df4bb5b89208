import React from 'react';
import { TagCloudData } from '../types';

interface TagCloudProps {
  tags: TagCloudData[];
  selectedTags: string[];
  onTagClick: (tagName: string) => void;
  className?: string;
}

const TagCloud: React.FC<TagCloudProps> = ({
  tags,
  selectedTags,
  onTagClick,
  className = '',
}) => {
  if (tags.length === 0) {
    return (
      <div className={`text-center py-8 text-gray-500 ${className}`}>
        No tags available
      </div>
    );
  }

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {tags.map((tag) => {
        const isSelected = selectedTags.includes(tag.name);
        
        return (
          <button
            key={tag.id}
            onClick={() => onTagClick(tag.name)}
            className={`
              inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
              transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2
              ${isSelected 
                ? 'ring-2 ring-offset-2 ring-primary-500 shadow-lg' 
                : 'hover:shadow-md'
              }
            `}
            style={{
              backgroundColor: tag.color,
              color: '#ffffff',
              fontSize: `${Math.max(12, Math.min(16, tag.size / 3))}px`,
            }}
            title={tag.description || `${tag.usage_count} videos tagged`}
          >
            {tag.name}
            <span className="ml-1 text-xs opacity-75">
              {tag.usage_count}
            </span>
          </button>
        );
      })}
    </div>
  );
};

export default TagCloud;
