import React, { useState } from 'react';
import { 
  DocumentDuplicateIcon, 
  MagnifyingGlassIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

interface TranscriptViewerProps {
  transcript: string;
  className?: string;
}

const TranscriptViewer: React.FC<TranscriptViewerProps> = ({ 
  transcript, 
  className = '' 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const handleCopyTranscript = async () => {
    try {
      await navigator.clipboard.writeText(transcript);
      toast.success('Transcript copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy transcript');
    }
  };

  const highlightSearchTerm = (text: string, term: string): React.ReactNode => {
    if (!term.trim()) return text;

    const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    );
  };

  const getDisplayText = () => {
    if (isExpanded) return transcript;
    return transcript.length > 300 ? transcript.substring(0, 300) + '...' : transcript;
  };

  const searchMatches = searchTerm.trim() 
    ? (transcript.match(new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi')) || []).length
    : 0;

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Search and Actions */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search in transcript..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
        <button
          onClick={handleCopyTranscript}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          title="Copy transcript"
        >
          <DocumentDuplicateIcon className="h-4 w-4" />
        </button>
      </div>

      {/* Search Results Info */}
      {searchTerm.trim() && (
        <div className="text-xs text-gray-500">
          {searchMatches > 0 ? (
            `Found ${searchMatches} match${searchMatches !== 1 ? 'es' : ''}`
          ) : (
            'No matches found'
          )}
        </div>
      )}

      {/* Transcript Content */}
      <div className="relative">
        <div className={`
          prose prose-sm max-w-none text-gray-700 leading-relaxed
          ${!isExpanded ? 'max-h-32 overflow-hidden' : ''}
        `}>
          <p className="whitespace-pre-wrap">
            {highlightSearchTerm(getDisplayText(), searchTerm)}
          </p>
        </div>

        {/* Expand/Collapse Button */}
        {transcript.length > 300 && (
          <div className="mt-2">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="inline-flex items-center text-sm text-primary-600 hover:text-primary-700 font-medium"
            >
              {isExpanded ? (
                <>
                  <ChevronUpIcon className="h-4 w-4 mr-1" />
                  Show Less
                </>
              ) : (
                <>
                  <ChevronDownIcon className="h-4 w-4 mr-1" />
                  Show More
                </>
              )}
            </button>
          </div>
        )}

        {/* Fade overlay when collapsed */}
        {!isExpanded && transcript.length > 300 && (
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none" />
        )}
      </div>

      {/* Transcript Stats */}
      <div className="text-xs text-gray-500 border-t border-gray-100 pt-2">
        {transcript.split(/\s+/).length} words • {transcript.length} characters
      </div>
    </div>
  );
};

export default TranscriptViewer;
