import React from 'react';

interface TagData {
  id: number;
  name: string;
  color: string;
  usage_count: number;
  description?: string;
}

interface TagChartProps {
  tags: TagData[];
  className?: string;
}

const TagChart: React.FC<TagChartProps> = ({ tags, className = '' }) => {
  if (tags.length === 0) {
    return (
      <div className={`text-center py-8 text-gray-500 ${className}`}>
        No tag data available
      </div>
    );
  }

  const maxUsage = Math.max(...tags.map(tag => tag.usage_count));

  return (
    <div className={`space-y-3 ${className}`}>
      {tags.map((tag) => {
        const percentage = maxUsage > 0 ? (tag.usage_count / maxUsage) * 100 : 0;
        
        return (
          <div key={tag.id} className="flex items-center space-x-3">
            <div className="flex-1">
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: tag.color }}
                  />
                  <span className="text-sm font-medium text-gray-900">
                    {tag.name}
                  </span>
                </div>
                <span className="text-sm text-gray-500">
                  {tag.usage_count}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${percentage}%`,
                    backgroundColor: tag.color 
                  }}
                />
              </div>
              {tag.description && (
                <p className="text-xs text-gray-500 mt-1">{tag.description}</p>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default TagChart;
