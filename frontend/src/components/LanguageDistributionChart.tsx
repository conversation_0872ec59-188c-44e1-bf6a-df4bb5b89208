import React from 'react';

interface LanguageDistributionChartProps {
  data: Record<string, number>;
  className?: string;
}

const LanguageDistributionChart: React.FC<LanguageDistributionChartProps> = ({ 
  data, 
  className = '' 
}) => {
  const entries = Object.entries(data);
  
  if (entries.length === 0) {
    return (
      <div className={`text-center py-8 text-gray-500 ${className}`}>
        No language data available
      </div>
    );
  }

  const total = entries.reduce((sum, [, count]) => sum + count, 0);
  
  // Sort by count descending
  const sortedEntries = entries.sort(([, a], [, b]) => b - a);

  const getLanguageName = (code: string): string => {
    const languageNames: Record<string, string> = {
      'en': 'English',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'zh': 'Chinese',
      'ja': 'Japanese',
      'ko': 'Korean',
      'ru': 'Russian',
      'ar': 'Arabic',
      'hi': 'Hindi',
      'unknown': 'Unknown',
    };
    return languageNames[code] || code.toUpperCase();
  };

  const colors = [
    'bg-blue-500',
    'bg-green-500',
    'bg-yellow-500',
    'bg-purple-500',
    'bg-pink-500',
    'bg-indigo-500',
    'bg-red-500',
    'bg-orange-500',
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Pie Chart Representation (Simple Bars) */}
      <div className="space-y-2">
        {sortedEntries.map(([language, count], index) => {
          const percentage = total > 0 ? (count / total) * 100 : 0;
          const colorClass = colors[index % colors.length];
          
          return (
            <div key={language} className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 w-24">
                <div className={`w-3 h-3 rounded ${colorClass}`} />
                <span className="text-sm font-medium text-gray-700">
                  {getLanguageName(language)}
                </span>
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                    <div
                      className={`h-4 rounded-full ${colorClass} transition-all duration-300`}
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                  <div className="w-16 text-sm text-gray-500 text-right">
                    {count} ({percentage.toFixed(1)}%)
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary Stats */}
      <div className="border-t border-gray-200 pt-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Total Videos:</span>
            <span className="ml-2 font-medium">{total}</span>
          </div>
          <div>
            <span className="text-gray-500">Languages:</span>
            <span className="ml-2 font-medium">{entries.length}</span>
          </div>
          <div>
            <span className="text-gray-500">Most Common:</span>
            <span className="ml-2 font-medium">
              {sortedEntries.length > 0 
                ? getLanguageName(sortedEntries[0][0])
                : 'N/A'
              }
            </span>
          </div>
          <div>
            <span className="text-gray-500">Diversity:</span>
            <span className="ml-2 font-medium">
              {entries.length > 1 ? 'Multilingual' : 'Single Language'}
            </span>
          </div>
        </div>
      </div>

      {/* Detailed Breakdown */}
      {sortedEntries.length > 3 && (
        <div className="text-xs text-gray-500">
          <p>
            Top 3 languages account for{' '}
            {(
              (sortedEntries.slice(0, 3).reduce((sum, [, count]) => sum + count, 0) / total) * 100
            ).toFixed(1)}%{' '}
            of all videos
          </p>
        </div>
      )}
    </div>
  );
};

export default LanguageDistributionChart;
