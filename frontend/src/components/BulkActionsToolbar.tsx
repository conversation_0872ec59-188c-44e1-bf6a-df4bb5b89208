import React, { useState } from 'react';
import {
  XMarkIcon,
  TagIcon,
  TrashIcon,
  CheckIcon,
  MinusIcon,
  EllipsisHorizontalIcon
} from '@heroicons/react/24/outline';
import { useVideoSelection } from '../contexts/VideoSelectionContext';

interface BulkActionsToolbarProps {
  onAddTags: () => void;
  onRemoveTags: () => void;
  onDelete: () => void;
  totalVideos: number;
}

const BulkActionsToolbar: React.FC<BulkActionsToolbarProps> = ({
  onAddTags,
  onRemoveTags,
  onDelete,
  totalVideos
}) => {
  const {
    getSelectedCount,
    selectAllVideos,
    deselectAllVideos,
    exitSelectionMode,
    isSelectionMode
  } = useVideoSelection();

  const [showMoreActions, setShowMoreActions] = useState(false);
  const selectedCount = getSelectedCount();

  if (!isSelectionMode || selectedCount === 0) {
    return null;
  }

  const handleSelectAll = () => {
    // This would need to be passed the current video IDs from the parent
    // For now, we'll emit an event that the parent can listen to
    const event = new CustomEvent('selectAllVideos');
    window.dispatchEvent(event);
  };

  const isAllSelected = selectedCount === totalVideos;

  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-900/40 border border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center space-x-4">
          {/* Selection Info */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {selectedCount} selected
            </span>
            
            {/* Select All / Deselect All */}
            <button
              onClick={isAllSelected ? deselectAllVideos : handleSelectAll}
              className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium"
            >
              {isAllSelected ? 'Deselect All' : 'Select All'}
            </button>
          </div>

          {/* Divider */}
          <div className="h-6 w-px bg-gray-300 dark:bg-gray-600" />

          {/* Primary Actions */}
          <div className="flex items-center space-x-2">
            {/* Add Tags */}
            <button
              onClick={onAddTags}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800 transition-colors"
            >
              <TagIcon className="h-4 w-4 mr-1" />
              Add Tags
            </button>

            {/* Remove Tags */}
            <button
              onClick={onRemoveTags}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800 transition-colors"
            >
              <MinusIcon className="h-4 w-4 mr-1" />
              Remove Tags
            </button>

            {/* More Actions */}
            <div className="relative">
              <button
                onClick={() => setShowMoreActions(!showMoreActions)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800 transition-colors"
              >
                <EllipsisHorizontalIcon className="h-4 w-4" />
              </button>

              {/* More Actions Dropdown */}
              {showMoreActions && (
                <div className="absolute bottom-full mb-2 right-0 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg dark:shadow-gray-900/40 border border-gray-200 dark:border-gray-700 py-1">
                  <button
                    onClick={() => {
                      onDelete();
                      setShowMoreActions(false);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center"
                  >
                    <TrashIcon className="h-4 w-4 mr-2" />
                    Delete Videos
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Divider */}
          <div className="h-6 w-px bg-gray-300 dark:bg-gray-600" />

          {/* Exit Selection Mode */}
          <button
            onClick={exitSelectionMode}
            className="p-2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            title="Exit selection mode"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default BulkActionsToolbar;
