import React, { useState } from 'react';
import {
  CakeIcon,
  ClockIcon,
  UserGroupIcon,
  StarIcon,
  DocumentArrowDownIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from '@heroicons/react/24/outline';
import { Recipe } from '../types';

interface RecipeCardProps {
  recipe: Recipe;
  className?: string;
}

const RecipeCard: React.FC<RecipeCardProps> = ({ recipe, className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getDifficultyColor = (difficulty?: string) => {
    if (!difficulty) return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    const lower = difficulty.toLowerCase();
    if (lower.includes('easy') || lower.includes('fácil')) 
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    if (lower.includes('medium') || lower.includes('medio')) 
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    if (lower.includes('hard') || lower.includes('difícil')) 
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  };

  const exportRecipe = () => {
    const recipeText = `
${recipe.title || 'Recipe'}
${recipe.description ? `\nDescription: ${recipe.description}` : ''}

${recipe.prep_time ? `Prep Time: ${recipe.prep_time}` : ''}
${recipe.cook_time ? `Cook Time: ${recipe.cook_time}` : ''}
${recipe.total_time ? `Total Time: ${recipe.total_time}` : ''}
${recipe.servings ? `Servings: ${recipe.servings}` : ''}
${recipe.difficulty ? `Difficulty: ${recipe.difficulty}` : ''}
${recipe.cuisine_type ? `Cuisine: ${recipe.cuisine_type}` : ''}

INGREDIENTS:
${recipe.ingredients.map((ing, index) => 
  `${index + 1}. ${ing.amount ? `${ing.amount} ${ing.unit || ''}`.trim() + ' ' : ''}${ing.name}${ing.notes ? ` (${ing.notes})` : ''}`
).join('\n')}

INSTRUCTIONS:
${recipe.instructions.map((step) => 
  `${step.step_number}. ${step.instruction}${step.time ? ` (${step.time})` : ''}${step.temperature ? ` at ${step.temperature}` : ''}`
).join('\n')}

Generated by tagTok - Video Recipe Extractor
    `.trim();

    const blob = new Blob([recipeText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${recipe.title || 'recipe'}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Recipe Header */}
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <CakeIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                {recipe.title || 'Untitled Recipe'}
              </h3>
              {recipe.description && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {recipe.description}
                </p>
              )}
            </div>
          </div>
          
          <button
            onClick={exportRecipe}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            title="Export Recipe"
          >
            <DocumentArrowDownIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Recipe Metadata */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          {recipe.total_time && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <ClockIcon className="h-4 w-4 mr-2" />
              <span>{recipe.total_time}</span>
            </div>
          )}
          
          {recipe.servings && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <UserGroupIcon className="h-4 w-4 mr-2" />
              <span>{recipe.servings}</span>
            </div>
          )}

          {recipe.cuisine_type && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <CakeIcon className="h-4 w-4 mr-2" />
              <span>{recipe.cuisine_type}</span>
            </div>
          )}

          {recipe.extraction_confidence && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <StarIcon className="h-4 w-4 mr-2" />
              <span>{Math.round(recipe.extraction_confidence * 100)}% confidence</span>
            </div>
          )}
        </div>

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {recipe.difficulty && (
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(recipe.difficulty)}`}>
              {recipe.difficulty}
            </span>
          )}
          
          <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            {recipe.ingredients.length} ingredients
          </span>
          
          <span className="px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
            {recipe.instructions.length} steps
          </span>
        </div>

        {/* Expand/Collapse Button */}
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full flex items-center justify-center gap-2 py-2 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
        >
          {isExpanded ? 'Hide Recipe Details' : 'Show Recipe Details'}
          {isExpanded ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
        </button>
      </div>

      {/* Expanded Recipe Details */}
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700 p-6 space-y-6">
          {/* Timing Information */}
          {(recipe.prep_time || recipe.cook_time) && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {recipe.prep_time && (
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="text-sm text-gray-600 dark:text-gray-400">Prep Time</div>
                  <div className="font-semibold text-gray-900 dark:text-white">{recipe.prep_time}</div>
                </div>
              )}
              {recipe.cook_time && (
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="text-sm text-gray-600 dark:text-gray-400">Cook Time</div>
                  <div className="font-semibold text-gray-900 dark:text-white">{recipe.cook_time}</div>
                </div>
              )}
              {recipe.total_time && (
                <div className="text-center p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
                  <div className="text-sm text-primary-600 dark:text-primary-400">Total Time</div>
                  <div className="font-semibold text-primary-700 dark:text-primary-300">{recipe.total_time}</div>
                </div>
              )}
            </div>
          )}

          {/* Ingredients */}
          <div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Ingredients ({recipe.ingredients.length})
            </h4>
            <ul className="space-y-2">
              {recipe.ingredients.map((ingredient, index) => (
                <li key={index} className="flex items-start gap-3">
                  <span className="flex-shrink-0 w-6 h-6 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </span>
                  <div className="flex-1">
                    <span className="text-gray-900 dark:text-white">
                      {ingredient.amount && (
                        <span className="font-medium">
                          {ingredient.amount} {ingredient.unit || ''}{' '}
                        </span>
                      )}
                      {ingredient.name}
                    </span>
                    {ingredient.notes && (
                      <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
                        ({ingredient.notes})
                      </span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>

          {/* Instructions */}
          <div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Instructions ({recipe.instructions.length} steps)
            </h4>
            <ol className="space-y-4">
              {recipe.instructions.map((step) => (
                <li key={step.step_number} className="flex gap-4">
                  <span className="flex-shrink-0 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    {step.step_number}
                  </span>
                  <div className="flex-1">
                    <p className="text-gray-900 dark:text-white mb-1">
                      {step.instruction}
                    </p>
                    {(step.time || step.temperature) && (
                      <div className="flex gap-4 text-sm text-gray-600 dark:text-gray-400">
                        {step.time && (
                          <span className="flex items-center gap-1">
                            <ClockIcon className="h-3 w-3" />
                            {step.time}
                          </span>
                        )}
                        {step.temperature && (
                          <span>🌡️ {step.temperature}</span>
                        )}
                      </div>
                    )}
                  </div>
                </li>
              ))}
            </ol>
          </div>
        </div>
      )}
    </div>
  );
};

export default RecipeCard;
