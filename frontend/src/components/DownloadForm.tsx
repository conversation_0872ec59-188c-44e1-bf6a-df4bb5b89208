import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  LinkIcon,
  CloudArrowDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClipboardDocumentIcon
} from '@heroicons/react/24/outline';

import { videoApi } from '../utils/api';
import { DownloadRequest, DownloadResponse } from '../types';
import LoadingSpinner from './LoadingSpinner';

interface DownloadFormProps {
  onDownloadStart?: (response: DownloadResponse) => void;
  disabled?: boolean;
  className?: string;
}

const DownloadForm: React.FC<DownloadFormProps> = ({
  onDownloadStart,
  disabled = false,
  className = '',
}) => {
  const [url, setUrl] = useState('');
  const [quality, setQuality] = useState('best');
  const [format, setFormat] = useState('mp4');
  const [isValidUrl, setIsValidUrl] = useState<boolean | null>(null);
  const [isPasting, setIsPasting] = useState(false);

  const downloadMutation = useMutation({
    mutationFn: (downloadRequest: DownloadRequest) => {
      return videoApi.downloadVideo(downloadRequest);
    },
    onSuccess: (response: DownloadResponse) => {
      toast.success('Download started successfully!');
      setUrl('');
      setIsValidUrl(null);
      if (onDownloadStart) {
        onDownloadStart(response);
      }
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.detail || error.message || 'Unknown error occurred';

      // Provide user-friendly error messages
      let friendlyMessage = errorMessage;
      if (errorMessage.includes('Invalid or unsupported URL')) {
        friendlyMessage = 'This URL is not supported. Please try a YouTube, Vimeo, TikTok, Instagram, or Twitter link.';
      } else if (errorMessage.includes('Video unavailable') || errorMessage.includes('private')) {
        friendlyMessage = 'This video is private or unavailable. Please check the URL and try again.';
      } else if (errorMessage.includes('Age-restricted')) {
        friendlyMessage = 'Age-restricted videos cannot be downloaded.';
      } else if (errorMessage.includes('Premium content')) {
        friendlyMessage = 'Premium or subscription-only content cannot be downloaded.';
      } else if (errorMessage.includes('deleted')) {
        friendlyMessage = 'This video has been deleted and cannot be downloaded.';
      } else if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
        friendlyMessage = 'Network timeout. Please check your connection and try again.';
      } else if (errorMessage.includes('geo-blocked')) {
        friendlyMessage = 'This video is not available in your region.';
      }

      toast.error(friendlyMessage);
    },
  });

  const validateUrl = (inputUrl: string): boolean => {
    try {
      const urlObj = new URL(inputUrl);
      const supportedDomains = [
        'youtube.com', 'youtu.be', 'www.youtube.com',
        'vimeo.com', 'www.vimeo.com',
        'tiktok.com', 'www.tiktok.com',
        'instagram.com', 'www.instagram.com',
        'twitter.com', 'x.com', 'www.twitter.com'
      ];
      
      const domain = urlObj.hostname.toLowerCase();
      return supportedDomains.some(supported => domain.includes(supported));
    } catch {
      return false;
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setUrl(newUrl);

    if (newUrl.trim()) {
      setIsValidUrl(validateUrl(newUrl));
    } else {
      setIsValidUrl(null);
    }
  };

  const handlePasteFromClipboard = async () => {
    if (downloadMutation.isPending || disabled || isPasting) return;

    setIsPasting(true);

    try {
      // Check if clipboard API is available
      if (!navigator.clipboard || !navigator.clipboard.readText) {
        toast.error('Clipboard access not available on this device');
        return;
      }

      // Request clipboard permission and read text
      const clipboardText = await navigator.clipboard.readText();

      if (!clipboardText.trim()) {
        toast.error('Clipboard is empty');
        return;
      }

      // Set the URL and validate it
      setUrl(clipboardText.trim());

      if (clipboardText.trim()) {
        setIsValidUrl(validateUrl(clipboardText.trim()));
      } else {
        setIsValidUrl(null);
      }

      toast.success('URL pasted from clipboard');

    } catch (error) {
      // Handle different types of clipboard errors
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          toast.error('Clipboard access denied. Please grant permission or paste manually.');
        } else if (error.name === 'NotFoundError') {
          toast.error('No text found in clipboard');
        } else {
          toast.error('Failed to read from clipboard');
        }
      } else {
        toast.error('Failed to access clipboard');
      }
    } finally {
      setIsPasting(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!url.trim()) {
      toast.error('Please enter a URL');
      return;
    }
    
    if (!isValidUrl) {
      toast.error('Please enter a valid URL from a supported site');
      return;
    }

    downloadMutation.mutate({
      url: url.trim(),
      quality,
      format,
    });
  };

  const getUrlStatusIcon = () => {
    if (isValidUrl === null) return null;
    
    if (isValidUrl) {
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
    } else {
      return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
    }
  };

  const canSubmit = url.trim() && isValidUrl && !downloadMutation.isPending && !disabled;

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <div className="flex items-center space-x-2 mb-4">
        <CloudArrowDownIcon className="h-6 w-6 text-primary-600" />
        <h3 className="text-lg font-medium text-gray-900">Download from URL</h3>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* URL Input */}
        <div>
          <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
            Video URL
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <LinkIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="url"
              id="url"
              value={url}
              onChange={handleUrlChange}
              placeholder="https://youtube.com/watch?v=..."
              disabled={downloadMutation.isPending || disabled}
              className={`
                block w-full pl-10 pr-20 py-2 border rounded-md shadow-sm text-base
                focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500
                disabled:opacity-50 disabled:cursor-not-allowed
                ${isValidUrl === false
                  ? 'border-red-300 bg-red-50'
                  : isValidUrl === true
                  ? 'border-green-300 bg-green-50'
                  : 'border-gray-300'
                }
              `}
              style={{
                fontSize: '16px', // Prevents zoom on iOS
                WebkitAppearance: 'none',
                borderRadius: '6px'
              }}
            />

            {/* Paste Button */}
            <div className="absolute inset-y-0 right-0 flex items-center">
              <button
                type="button"
                onClick={handlePasteFromClipboard}
                disabled={downloadMutation.isPending || disabled || isPasting}
                className={`
                  mr-2 p-1.5 rounded-md transition-colors duration-200
                  ${downloadMutation.isPending || disabled || isPasting
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-400 hover:text-primary-600 hover:bg-primary-50 active:bg-primary-100'
                  }
                `}
                title="Paste from clipboard"
                aria-label="Paste URL from clipboard"
              >
                <ClipboardDocumentIcon className={`h-5 w-5 ${isPasting ? 'animate-pulse' : ''}`} />
              </button>

              {/* URL Status Icon */}
              {getUrlStatusIcon() && (
                <div className="mr-3">
                  {getUrlStatusIcon()}
                </div>
              )}
            </div>
          </div>
          
          {/* URL validation message */}
          {isValidUrl === false && (
            <p className="mt-1 text-sm text-red-600">
              Please enter a valid URL from YouTube, Vimeo, TikTok, Instagram, or Twitter
            </p>
          )}
          
          {/* Supported sites info */}
          <p className="mt-1 text-xs text-gray-500">
            Supported sites: YouTube, Vimeo, TikTok, Instagram, Twitter
          </p>
        </div>

        {/* Quality Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="quality" className="block text-sm font-medium text-gray-700 mb-1">
              Quality
            </label>
            <select
              id="quality"
              value={quality}
              onChange={(e) => setQuality(e.target.value)}
              disabled={downloadMutation.isPending || disabled}
              className="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
            >
              <option value="best[height<=480]">480p (SD)</option>
              <option value="best[height<=720]">720p (HD)</option>
              <option value="best[height<=1080]">1080p (Full HD)</option>
              <option value="best">Best Available</option>
            </select>
          </div>

          <div>
            <label htmlFor="format" className="block text-sm font-medium text-gray-700 mb-1">
              Format
            </label>
            <select
              id="format"
              value={format}
              onChange={(e) => setFormat(e.target.value)}
              disabled={downloadMutation.isPending || disabled}
              className="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
            >
              <option value="mp4">MP4</option>
              <option value="webm">WebM</option>
              <option value="mkv">MKV</option>
            </select>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={!canSubmit}
            className={`
              px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500
              ${canSubmit
                ? 'bg-primary-600 text-white hover:bg-primary-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }
            `}
          >
            {downloadMutation.isPending ? (
              <div className="flex items-center space-x-2">
                <LoadingSpinner size="sm" />
                <span>Starting Download...</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <CloudArrowDownIcon className="h-4 w-4" />
                <span>Download Video</span>
              </div>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default DownloadForm;
