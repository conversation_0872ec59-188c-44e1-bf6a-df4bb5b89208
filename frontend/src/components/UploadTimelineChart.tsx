import React from 'react';
import { format, parseISO } from 'date-fns';

interface TimelineData {
  date: string;
  count: number;
}

interface UploadTimelineChartProps {
  data: TimelineData[];
  className?: string;
}

const UploadTimelineChart: React.FC<UploadTimelineChartProps> = ({ 
  data, 
  className = '' 
}) => {
  if (data.length === 0) {
    return (
      <div className={`text-center py-8 text-gray-500 ${className}`}>
        No upload data available
      </div>
    );
  }

  const maxCount = Math.max(...data.map(item => item.count));
  const chartHeight = 200;

  return (
    <div className={`${className}`}>
      <div className="flex items-end space-x-1 h-48 mb-4">
        {data.map((item, index) => {
          const height = maxCount > 0 ? (item.count / maxCount) * chartHeight : 0;
          
          return (
            <div
              key={item.date}
              className="flex-1 flex flex-col items-center group"
            >
              <div
                className="w-full bg-primary-500 rounded-t hover:bg-primary-600 transition-colors cursor-pointer relative"
                style={{ height: `${height}px` }}
                title={`${format(parseISO(item.date), 'MMM d, yyyy')}: ${item.count} video${item.count !== 1 ? 's' : ''}`}
              >
                {/* Tooltip on hover */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                  {format(parseISO(item.date), 'MMM d')}: {item.count}
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      {/* X-axis labels */}
      <div className="flex justify-between text-xs text-gray-500">
        {data.length > 0 && (
          <>
            <span>{format(parseISO(data[0].date), 'MMM d')}</span>
            {data.length > 2 && (
              <span>{format(parseISO(data[Math.floor(data.length / 2)].date), 'MMM d')}</span>
            )}
            <span>{format(parseISO(data[data.length - 1].date), 'MMM d')}</span>
          </>
        )}
      </div>
      
      {/* Summary */}
      <div className="mt-4 text-sm text-gray-600">
        <p>
          Total uploads: {data.reduce((sum, item) => sum + item.count, 0)} videos
          {data.length > 1 && (
            <>
              {' • '}
              Average: {(data.reduce((sum, item) => sum + item.count, 0) / data.length).toFixed(1)} per day
            </>
          )}
        </p>
      </div>
    </div>
  );
};

export default UploadTimelineChart;
