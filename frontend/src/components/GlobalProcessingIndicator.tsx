import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { CogIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { analyticsApi } from '../utils/api';

interface ProcessingStats {
  [key: string]: number; // The API returns { "processing": 2, "pending": 1, "failed": 0, etc. }
}

const GlobalProcessingIndicator: React.FC = () => {
  const { data: stats, isLoading, error } = useQuery<ProcessingStats>({
    queryKey: ['processingStats'],
    queryFn: () => analyticsApi.getProcessingStatus(),
    refetchInterval: (query) => {
      // Stop polling if no videos are processing
      const data = query.state.data;
      if (data) {
        const processingCount = data.processing || 0;
        const pendingCount = data.pending || 0;
        if (processingCount === 0 && pendingCount === 0) {
          return false;
        }
      }
      // Poll every 10 seconds when there's processing activity
      return 10000;
    },
    retry: 1,
    staleTime: 5000,
  });

  // Don't show anything if there's no processing activity or if there's an error
  if (isLoading || error || !stats) {
    return null;
  }

  const processingCount = stats.processing || 0;
  const pendingCount = stats.pending || 0;
  const failedCount = stats.failed || 0;
  const totalActive = processingCount + pendingCount;

  // Don't show if no active processing
  if (totalActive === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2 max-w-sm">
        <CogIcon className="h-5 w-5 animate-spin" />
        <div className="text-sm">
          <div className="font-medium">
            Processing {totalActive} video{totalActive !== 1 ? 's' : ''}
          </div>
          {failedCount > 0 && (
            <div className="flex items-center gap-1 text-red-200">
              <ExclamationTriangleIcon className="h-3 w-3" />
              <span className="text-xs">{failedCount} failed</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GlobalProcessingIndicator;
