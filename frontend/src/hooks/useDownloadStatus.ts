import { useQuery } from '@tanstack/react-query';
import { videoApi } from '../utils/api';

interface DownloadStatus {
  video_id: number;
  download_status: string;
  download_progress: number;
  download_error?: string;
  processing_status: string;
  processing_progress: number;
}

export const useDownloadStatus = (videoId: number, enabled: boolean = true) => {
  return useQuery<DownloadStatus>({
    queryKey: ['downloadStatus', videoId],
    queryFn: () => videoApi.getDownloadStatus(videoId),
    enabled: enabled && videoId > 0,
    refetchInterval: (query) => {
      // Stop polling if download is completed or failed
      const data = query.state.data;
      if (data?.download_status === 'completed' || data?.download_status === 'failed') {
        return false;
      }
      // Poll every 3 seconds while downloading (less aggressive)
      return 3000;
    },
    retry: 2, // Reduced retries
    retryDelay: 2000,
    staleTime: 1000, // Consider data stale after 1 second
    gcTime: 5000, // Keep in cache for 5 seconds
  });
};

export default useDownloadStatus;
