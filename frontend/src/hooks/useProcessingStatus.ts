import { useQuery } from '@tanstack/react-query';
import { videoApi } from '../utils/api';

interface ProcessingStatus {
  video_id: number;
  processing_status: string;
  processing_progress: number;
  processed: boolean;
}

export const useProcessingStatus = (videoId: number, enabled: boolean = true) => {
  return useQuery<ProcessingStatus>({
    queryKey: ['processingStatus', videoId],
    queryFn: () => videoApi.getProcessingStatus(videoId),
    enabled: enabled && videoId > 0,
    refetchInterval: (query) => {
      // Stop polling if processing is completed or failed
      const data = query.state.data;
      if (data?.processing_status === 'completed' || data?.processing_status === 'failed' || data?.processed) {
        return false;
      }
      // Poll every 5 seconds while processing (less aggressive than download status)
      return 5000;
    },
    retry: 2, // Reduced retries for processing status
    retryDelay: 2000,
    staleTime: 2000, // Consider data stale after 2 seconds
    gcTime: 10000, // Keep in cache for 10 seconds
  });
};

export default useProcessingStatus;
