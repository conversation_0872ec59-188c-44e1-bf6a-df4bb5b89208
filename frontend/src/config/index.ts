/**
 * Centralized configuration management for tagTok frontend
 */

export interface AppConfig {
  // Port configuration
  backendExternalPort: string;
  frontendExternalPort: string;
  nginxPort: string;
  
  // API configuration
  apiUrl: string;
  
  // Environment
  isDevelopment: boolean;
  isProduction: boolean;
}

class ConfigManager {
  private static instance: ConfigManager;
  private config: AppConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private loadConfig(): AppConfig {
    // Get environment variables with defaults
    const backendExternalPort = process.env.REACT_APP_BACKEND_EXTERNAL_PORT || '8080';
    const frontendExternalPort = process.env.REACT_APP_FRONTEND_EXTERNAL_PORT || '3001';
    const nginxPort = process.env.REACT_APP_NGINX_PORT || '8790';
    
    const isDevelopment = process.env.NODE_ENV === 'development';
    const isProduction = process.env.NODE_ENV === 'production';

    return {
      backendExternalPort,
      frontendExternalPort,
      nginxPort,
      apiUrl: this.determineApiUrl(backendExternalPort, nginxPort),
      isDevelopment,
      isProduction,
    };
  }

  private determineApiUrl(backendPort: string, nginxPort: string): string {
    // Get current location details
    const hostname = window.location.hostname;
    const protocol = window.location.protocol;

    // Check if accessing via IP address (local network access)
    const isIpAddress = /^\d+\.\d+\.\d+\.\d+$/.test(hostname);

    // If accessing via IP address, always use local API endpoint to avoid CORS
    if (isIpAddress) {
      // Check if we're accessing through frontend port (development frontend)
      if (window.location.port === this.config?.frontendExternalPort) {
        // Direct backend access (no /api prefix)
        return `${protocol}//${hostname}:${backendPort}`;
      } else {
        // Through nginx proxy (with /api prefix)
        return `${protocol}//${hostname}:${nginxPort}/api`;
      }
    }

    // Check environment variable for domain-based access
    const envApiUrl = process.env.REACT_APP_API_URL;
    if (envApiUrl && (hostname !== 'localhost' && hostname !== '127.0.0.1')) {
      return envApiUrl;
    }

    // Default to localhost for desktop development
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return `http://localhost:${backendPort}`;
    } else {
      // Fallback for other domain access
      return `${protocol}//${hostname}/api`;
    }
  }

  public getConfig(): AppConfig {
    return this.config;
  }

  public getApiUrl(): string {
    return this.config.apiUrl;
  }

  public printConfig(): void {
    console.log('=== tagTok Frontend Configuration ===');
    console.log('Backend External Port:', this.config.backendExternalPort);
    console.log('Frontend External Port:', this.config.frontendExternalPort);
    console.log('Nginx Port:', this.config.nginxPort);
    console.log('API URL:', this.config.apiUrl);
    console.log('Environment:', this.config.isDevelopment ? 'Development' : 'Production');
    console.log('=====================================');
  }
}

// Export singleton instance
export const configManager = ConfigManager.getInstance();
export const config = configManager.getConfig();

// Export commonly used values
export const API_BASE_URL = configManager.getApiUrl();
export const IS_DEVELOPMENT = config.isDevelopment;
export const IS_PRODUCTION = config.isProduction;
