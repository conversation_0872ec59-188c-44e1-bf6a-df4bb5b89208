import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,
  TagIcon,
  ClockIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

import { videoApi, tagApi } from '../utils/api';
import { Video } from '../types';
import VideoPlayer from '../components/VideoPlayer';
import TagManager from '../components/TagManager';
import TranscriptViewer from '../components/TranscriptViewer';
import RecipeCard from '../components/RecipeCard';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const VideoDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState('');

  const videoId = parseInt(id || '0');

  // Fetch video details
  const {
    data: video,
    isPending: isLoading,
    error,
    refetch,
  } = useQuery<Video>({
    queryKey: ['video', videoId],
    queryFn: () => videoApi.getVideo(videoId),
    enabled: !!videoId,
  });

  // Update video mutation
  const updateMutation = useMutation({
    mutationFn: (data: { title: string }) => videoApi.updateVideo(videoId, data),
    onSuccess: () => {
      toast.success('Video updated successfully');
      setIsEditing(false);
      // Invalidate all video-related queries with broader patterns
      queryClient.invalidateQueries({ queryKey: ['video', videoId] });
      queryClient.invalidateQueries({ queryKey: ['videos'] });
      queryClient.invalidateQueries({ queryKey: ['tagCloud'] });
      // Also invalidate any specific video queries
      queryClient.invalidateQueries({ predicate: (query) =>
        query.queryKey[0] === 'videos' || query.queryKey[0] === 'video'
      });
    },
    onError: (error: any) => {
      toast.error('Failed to update video: ' + (error.response?.data?.detail || error.message));
    },
  });

  // Delete video mutation
  const deleteMutation = useMutation({
    mutationFn: () => videoApi.deleteVideo(videoId),
    onSuccess: () => {
      toast.success('Video deleted successfully');
      // Invalidate all video-related queries with broader patterns
      queryClient.invalidateQueries({ queryKey: ['videos'] });
      queryClient.invalidateQueries({ queryKey: ['video'] });
      queryClient.invalidateQueries({ queryKey: ['tagCloud'] });
      // Use predicate to catch all video-related queries
      queryClient.invalidateQueries({ predicate: (query) =>
        query.queryKey[0] === 'videos' || query.queryKey[0] === 'video'
      });
      navigate('/');
    },
    onError: (error: any) => {
      toast.error('Failed to delete video: ' + (error.response?.data?.detail || error.message));
    },
  });

  // Reprocess video mutation
  const reprocessMutation = useMutation({
    mutationFn: () => videoApi.reprocessVideo(videoId),
    onSuccess: () => {
      toast.success('Video queued for reprocessing');
      // Invalidate video queries to reflect processing status changes
      queryClient.invalidateQueries({ queryKey: ['video', videoId] });
      queryClient.invalidateQueries({ queryKey: ['videos'] });
      queryClient.invalidateQueries({ predicate: (query) =>
        query.queryKey[0] === 'videos' || query.queryKey[0] === 'video'
      });
    },
    onError: (error: any) => {
      toast.error('Failed to reprocess video: ' + (error.response?.data?.detail || error.message));
    },
  });

  const handleSaveTitle = () => {
    if (editTitle.trim() !== video?.title) {
      updateMutation.mutate({ title: editTitle.trim() });
    } else {
      setIsEditing(false);
    }
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
      deleteMutation.mutate();
    }
  };

  const handleDownload = async () => {
    try {
      const blob = await videoApi.downloadVideoFile(videoId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = video?.original_filename || 'video.mp4';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Download started');
    } catch (error: any) {
      toast.error('Download failed: ' + (error.response?.data?.detail || error.message));
    }
  };

  const formatFileSize = (bytes: number): string => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !video) {
    return (
      <ErrorMessage 
        message="Failed to load video details" 
        onRetry={() => refetch()}
      />
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => navigate('/')}
          className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Videos
        </button>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => reprocessMutation.mutate()}
            disabled={reprocessMutation.isPending}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-1 ${reprocessMutation.isPending ? 'animate-spin' : ''}`} />
            Reprocess
          </button>
          
          <button
            onClick={handleDownload}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
            Download
          </button>
          
          <button
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
            className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            <TrashIcon className="h-4 w-4 mr-1" />
            Delete
          </button>
        </div>
      </div>

      {/* Video Title */}
      <div className="bg-white rounded-lg shadow p-6">
        {isEditing ? (
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              className="flex-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              onKeyPress={(e) => e.key === 'Enter' && handleSaveTitle()}
              autoFocus
            />
            <button
              onClick={handleSaveTitle}
              disabled={updateMutation.isPending}
              className="px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              Save
            </button>
            <button
              onClick={() => setIsEditing(false)}
              className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </button>
          </div>
        ) : (
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">{video.title}</h1>
            <button
              onClick={() => {
                setEditTitle(video.title);
                setIsEditing(true);
              }}
              className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
            >
              <PencilIcon className="h-4 w-4 mr-1" />
              Edit
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Video Player */}
        <div className="lg:col-span-2 space-y-6">
          <VideoPlayer video={video} />
          
          {/* Video Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Video Information</h2>
            <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <dt className="text-sm font-medium text-gray-500">Original Filename</dt>
                <dd className="mt-1 text-sm text-gray-900">{video.original_filename}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Upload Date</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {format(new Date(video.upload_date), 'PPP')}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Duration</dt>
                <dd className="mt-1 text-sm text-gray-900">{formatDuration(video.duration)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">File Size</dt>
                <dd className="mt-1 text-sm text-gray-900">{formatFileSize(video.file_size)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Resolution</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {video.width && video.height ? `${video.width}×${video.height}` : 'Unknown'}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Processing Status</dt>
                <dd className="mt-1">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    video.processing_status === 'completed' 
                      ? 'bg-green-100 text-green-800'
                      : video.processing_status === 'processing'
                      ? 'bg-blue-100 text-blue-800'
                      : video.processing_status === 'failed'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {video.processing_status}
                  </span>
                </dd>
              </div>
            </dl>
          </div>

          {/* Recipe Card */}
          {video.recipe && (
            <RecipeCard recipe={video.recipe} />
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Tags */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <TagIcon className="h-5 w-5 text-gray-400 mr-2" />
              <h2 className="text-lg font-medium text-gray-900">Tags</h2>
            </div>
            <TagManager videoId={videoId} tags={video.tags} />
          </div>

          {/* Transcript */}
          {video.transcript && (
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center mb-4">
                <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-2" />
                <h2 className="text-lg font-medium text-gray-900">Transcript</h2>
                {video.transcript_language && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    {video.transcript_language.toUpperCase()}
                  </span>
                )}
              </div>
              <TranscriptViewer transcript={video.transcript} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VideoDetailPage;
