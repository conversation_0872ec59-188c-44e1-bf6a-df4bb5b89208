import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { CloudArrowDownIcon } from '@heroicons/react/24/outline';

import { DownloadResponse } from '../types';
import DownloadForm from '../components/DownloadForm';
import DownloadProgress from '../components/DownloadProgress';

const DownloadPage: React.FC = () => {
  const navigate = useNavigate();
  const [downloadingVideos, setDownloadingVideos] = useState<DownloadResponse[]>([]);

  const handleDownloadStart = (response: DownloadResponse) => {
    setDownloadingVideos(prev => [...prev, response]);
    
    // Navigate to home after starting download to see the video in the grid
    setTimeout(() => {
      navigate('/');
    }, 2000);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Download Videos</h1>
        <p className="mt-2 text-sm text-gray-700">
          Download videos from YouTube, TikTok, Instagram, and other platforms to automatically transcribe and tag them with AI
        </p>
      </div>

      {/* Download Form */}
      <DownloadForm
        onDownloadStart={handleDownloadStart}
        disabled={false}
      />

      {/* Download Instructions */}
      <div className="bg-green-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-green-900 mb-2">Supported Sites & Features</h3>
        <ul className="text-sm text-green-800 space-y-1">
          <li>• YouTube, Vimeo, TikTok, Instagram, Twitter/X</li>
          <li>• Automatic quality selection (up to 1080p)</li>
          <li>• Same AI processing as uploaded videos</li>
          <li>• Transcription and smart tagging included</li>
          <li>• Download progress tracking</li>
        </ul>
      </div>

      {/* Active Downloads */}
      {downloadingVideos.length > 0 && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Download Progress</h3>
          </div>
          <div className="p-6 space-y-4">
            {downloadingVideos.map((download, index) => (
              <DownloadProgress
                key={index}
                videoId={download.video_id}
                url={download.url}
                onComplete={() => {
                  // Remove from downloading list when completed
                  setDownloadingVideos(prev => 
                    prev.filter((_, i) => i !== index)
                  );
                }}
                onError={(error) => {
                  console.error('Download error:', error);
                }}
              />
            ))}
          </div>
        </div>
      )}

      {/* Tips Section */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 mb-2">Tips for Best Results</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Use direct video URLs for best compatibility</li>
          <li>• Public videos work better than private ones</li>
          <li>• Shorter videos (under 10 minutes) process faster</li>
          <li>• Check the video grid to see your downloaded content</li>
          <li>• Processing may take a few minutes depending on video length</li>
        </ul>
      </div>

      {/* Recent Downloads Info */}
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="flex items-center space-x-2 mb-2">
          <CloudArrowDownIcon className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-medium text-gray-900">What happens after download?</h3>
        </div>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• Videos are automatically transcribed using AI (Whisper)</li>
          <li>• Smart tags are generated based on the transcript content</li>
          <li>• Thumbnails are extracted for easy browsing</li>
          <li>• Downloaded videos appear in the main video grid</li>
          <li>• You can edit tags and add custom ones later</li>
        </ul>
      </div>
    </div>
  );
};

export default DownloadPage;
