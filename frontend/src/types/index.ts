export interface Video {
  id: number;
  filename: string;
  original_filename: string;
  title: string;
  file_path: string;
  file_size: number;
  duration?: number;
  width?: number;
  height?: number;
  fps?: number;
  thumbnail_path?: string;
  transcript?: string;
  transcript_language?: string;
  upload_date: string;
  processed: boolean;
  processing_status: string;
  processing_progress?: number;
  source_url?: string;
  download_status?: string;
  download_error?: string;
  download_progress?: number;
  tags: Tag[];
  recipe?: Recipe;
}

export interface Tag {
  id: number;
  name: string;
  color: string;
  description?: string;
  created_date: string;
  usage_count: number;
}

export interface VideoFilter {
  tags?: string[];
  search?: string;
  language?: string;
  processed?: boolean;
  date_from?: string;
  date_to?: string;
  category?: string;
}

export interface CategoryResponse {
  categories: string[];
  total: number;
}

export interface AnalyticsData {
  total_videos: number;
  total_tags: number;
  total_duration: number;
  processed_videos: number;
  pending_videos: number;
  top_tags: Array<{
    id: number;
    name: string;
    color: string;
    usage_count: number;
    description?: string;
  }>;
  language_distribution: Record<string, number>;
  upload_timeline: Array<{
    date: string;
    count: number;
  }>;
  duration_distribution: Record<string, number>;
}

export interface UploadResponse {
  message: string;
  uploaded_files: string[];
  failed_files: Array<{
    filename: string;
    error: string;
  }>;
  total_uploaded: number;
}

export interface ProcessingJob {
  id: number;
  video_id: number;
  job_type: string;
  status: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
}

export interface TagCloudData {
  id: number;
  name: string;
  color: string;
  description?: string;
  usage_count: number;
  size: number;
}

export interface ExportOptions {
  format: 'csv' | 'json';
  include_transcript: boolean;
  include_tags: boolean;
  tag_filter?: string[];
}

export interface ApiError {
  detail: string;
  error_code?: string;
}

export interface PaginationParams {
  skip: number;
  limit: number;
}

export interface VideoUpdateData {
  title?: string;
}

export interface TagCreateData {
  name: string;
  color: string;
  description?: string;
}

export interface TagUpdateData {
  name?: string;
  color?: string;
  description?: string;
}

export interface DownloadRequest {
  url: string;
  quality?: string;
  format?: string;
}

export interface DownloadResponse {
  message: string;
  video_id: number;
  download_status: string;
  url: string;
}

export interface Ingredient {
  name: string;
  amount?: string;
  unit?: string;
  notes?: string;
}

export interface InstructionStep {
  step_number: number;
  instruction: string;
  time?: string;
  temperature?: string;
}

export interface Recipe {
  id: number;
  video_id: number;
  title?: string;
  description?: string;
  ingredients: Ingredient[];
  instructions: InstructionStep[];
  prep_time?: string;
  cook_time?: string;
  total_time?: string;
  servings?: string;
  difficulty?: string;
  cuisine_type?: string;
  extracted_at: string;
  extraction_confidence?: number;
}

export interface RecipeWithVideo extends Recipe {
  video_title?: string;
  video_thumbnail_path?: string;
  video_duration?: number;
}
