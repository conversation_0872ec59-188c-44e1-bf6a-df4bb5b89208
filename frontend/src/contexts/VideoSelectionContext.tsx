import React, { createContext, useContext, useState, useCallback } from 'react';
import { Video } from '../types';

interface VideoSelectionContextType {
  selectedVideos: Set<number>;
  isSelectionMode: boolean;
  selectVideo: (videoId: number) => void;
  deselectVideo: (videoId: number) => void;
  toggleVideoSelection: (videoId: number) => void;
  selectAllVideos: (videoIds: number[]) => void;
  deselectAllVideos: () => void;
  enterSelectionMode: () => void;
  exitSelectionMode: () => void;
  isVideoSelected: (videoId: number) => boolean;
  getSelectedVideoIds: () => number[];
  getSelectedCount: () => number;
}

const VideoSelectionContext = createContext<VideoSelectionContextType | undefined>(undefined);

interface VideoSelectionProviderProps {
  children: React.ReactNode;
}

export const VideoSelectionProvider: React.FC<VideoSelectionProviderProps> = ({ children }) => {
  const [selectedVideos, setSelectedVideos] = useState<Set<number>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  const selectVideo = useCallback((videoId: number) => {
    setSelectedVideos(prev => new Set(prev).add(videoId));
    if (!isSelectionMode) {
      setIsSelectionMode(true);
    }
  }, [isSelectionMode]);

  const deselectVideo = useCallback((videoId: number) => {
    setSelectedVideos(prev => {
      const newSet = new Set(prev);
      newSet.delete(videoId);
      return newSet;
    });
  }, []);

  const toggleVideoSelection = useCallback((videoId: number) => {
    setSelectedVideos(prev => {
      const newSet = new Set(prev);
      if (newSet.has(videoId)) {
        newSet.delete(videoId);
      } else {
        newSet.add(videoId);
      }
      return newSet;
    });
    
    if (!isSelectionMode) {
      setIsSelectionMode(true);
    }
  }, [isSelectionMode]);

  const selectAllVideos = useCallback((videoIds: number[]) => {
    setSelectedVideos(new Set(videoIds));
    if (!isSelectionMode) {
      setIsSelectionMode(true);
    }
  }, [isSelectionMode]);

  const deselectAllVideos = useCallback(() => {
    setSelectedVideos(new Set());
  }, []);

  const enterSelectionMode = useCallback(() => {
    setIsSelectionMode(true);
  }, []);

  const exitSelectionMode = useCallback(() => {
    setIsSelectionMode(false);
    setSelectedVideos(new Set());
  }, []);

  const isVideoSelected = useCallback((videoId: number) => {
    return selectedVideos.has(videoId);
  }, [selectedVideos]);

  const getSelectedVideoIds = useCallback(() => {
    return Array.from(selectedVideos);
  }, [selectedVideos]);

  const getSelectedCount = useCallback(() => {
    return selectedVideos.size;
  }, [selectedVideos]);

  const value: VideoSelectionContextType = {
    selectedVideos,
    isSelectionMode,
    selectVideo,
    deselectVideo,
    toggleVideoSelection,
    selectAllVideos,
    deselectAllVideos,
    enterSelectionMode,
    exitSelectionMode,
    isVideoSelected,
    getSelectedVideoIds,
    getSelectedCount,
  };

  return (
    <VideoSelectionContext.Provider value={value}>
      {children}
    </VideoSelectionContext.Provider>
  );
};

export const useVideoSelection = (): VideoSelectionContextType => {
  const context = useContext(VideoSelectionContext);
  if (context === undefined) {
    throw new Error('useVideoSelection must be used within a VideoSelectionProvider');
  }
  return context;
};

export default VideoSelectionContext;
