#!/usr/bin/env python3
"""
Migration script to help users update their tagTok configuration
to use the new centralized port management system.
"""
import os
import shutil
import sys
from pathlib import Path


def backup_file(file_path: str) -> str:
    """Create a backup of the original file"""
    backup_path = f"{file_path}.backup"
    if os.path.exists(file_path):
        shutil.copy2(file_path, backup_path)
        print(f"✅ Backed up {file_path} to {backup_path}")
        return backup_path
    return ""


def migrate_env_file():
    """Migrate existing .env file to new format"""
    env_path = ".env"
    
    if not os.path.exists(env_path):
        print("ℹ️  No existing .env file found. Creating from .env.example...")
        if os.path.exists(".env.example"):
            shutil.copy2(".env.example", env_path)
            print(f"✅ Created {env_path} from .env.example")
        return
    
    # Backup existing file
    backup_path = backup_file(env_path)
    
    # Read existing configuration
    with open(env_path, 'r') as f:
        lines = f.readlines()
    
    # Parse existing values
    existing_config = {}
    for line in lines:
        line = line.strip()
        if line and not line.startswith('#') and '=' in line:
            key, value = line.split('=', 1)
            existing_config[key.strip()] = value.strip()
    
    # Map old configuration to new format
    port_mapping = {
        'BACKEND_PORT': 'BACKEND_EXTERNAL_PORT',
        'FRONTEND_PORT': 'FRONTEND_EXTERNAL_PORT', 
        'OLLAMA_PORT': 'OLLAMA_EXTERNAL_PORT',
    }
    
    # Update configuration
    new_config = {}
    
    # Copy existing values
    for key, value in existing_config.items():
        if key in port_mapping:
            new_key = port_mapping[key]
            new_config[new_key] = value
            print(f"🔄 Migrated {key}={value} to {new_key}={value}")
        else:
            new_config[key] = value
    
    # Add new configuration keys with defaults if not present
    defaults = {
        'NGINX_PORT': '8790',
        'BACKEND_EXTERNAL_PORT': '8080',
        'FRONTEND_EXTERNAL_PORT': '3001',
        'OLLAMA_EXTERNAL_PORT': '11435',
        'BACKEND_INTERNAL_PORT': '8000',
        'FRONTEND_INTERNAL_PORT': '80',
        'OLLAMA_INTERNAL_PORT': '11434',
        'BACKEND_URL': 'http://backend:8000',
        'OLLAMA_URL': 'http://ollama:11434',
        'FRONTEND_URL': 'http://frontend:80',
    }
    
    for key, default_value in defaults.items():
        if key not in new_config:
            new_config[key] = default_value
            print(f"➕ Added {key}={default_value}")
    
    # Write new configuration
    with open(env_path, 'w') as f:
        f.write("# tagTok Environment Configuration\n")
        f.write("# Migrated to centralized port management\n\n")
        
        f.write("# Database Configuration\n")
        f.write(f"DATABASE_URL={new_config.get('DATABASE_URL', 'sqlite:///db/tagTok.db')}\n\n")
        
        f.write("# Directory Paths\n")
        f.write(f"VIDEOS_DIR={new_config.get('VIDEOS_DIR', '/app/videos')}\n")
        f.write(f"TRANSCRIPTS_DIR={new_config.get('TRANSCRIPTS_DIR', '/app/transcripts')}\n\n")
        
        f.write("# Port Configuration - Centralized port management\n")
        f.write("# External ports (what users connect to)\n")
        f.write(f"NGINX_PORT={new_config.get('NGINX_PORT', '8790')}\n")
        f.write(f"BACKEND_EXTERNAL_PORT={new_config.get('BACKEND_EXTERNAL_PORT', '8080')}\n")
        f.write(f"FRONTEND_EXTERNAL_PORT={new_config.get('FRONTEND_EXTERNAL_PORT', '3001')}\n")
        f.write(f"OLLAMA_EXTERNAL_PORT={new_config.get('OLLAMA_EXTERNAL_PORT', '11435')}\n\n")
        
        f.write("# Internal service ports (inside Docker containers)\n")
        f.write(f"BACKEND_INTERNAL_PORT={new_config.get('BACKEND_INTERNAL_PORT', '8000')}\n")
        f.write(f"FRONTEND_INTERNAL_PORT={new_config.get('FRONTEND_INTERNAL_PORT', '80')}\n")
        f.write(f"OLLAMA_INTERNAL_PORT={new_config.get('OLLAMA_INTERNAL_PORT', '11434')}\n\n")
        
        f.write("# Service URLs for application code\n")
        f.write(f"BACKEND_URL={new_config.get('BACKEND_URL', 'http://backend:8000')}\n")
        f.write(f"OLLAMA_URL={new_config.get('OLLAMA_URL', 'http://ollama:11434')}\n")
        f.write(f"FRONTEND_URL={new_config.get('FRONTEND_URL', 'http://frontend:80')}\n\n")
        
        f.write("# API Configuration for frontend\n")
        f.write(f"REACT_APP_API_URL={new_config.get('REACT_APP_API_URL', 'http://localhost:8080')}\n\n")
        
        f.write("# AI Model Configuration\n")
        f.write(f"WHISPER_MODEL_SIZE={new_config.get('WHISPER_MODEL_SIZE', 'base')}\n\n")
        
        f.write("# Development Configuration\n")
        f.write(f"PYTHONPATH={new_config.get('PYTHONPATH', '/app')}\n")
        f.write(f"CHOKIDAR_USEPOLLING={new_config.get('CHOKIDAR_USEPOLLING', 'true')}\n\n")
        
        f.write("# Docker Configuration\n")
        f.write(f"COMPOSE_PROJECT_NAME={new_config.get('COMPOSE_PROJECT_NAME', 'tagtok')}\n")
    
    print(f"✅ Migrated .env file successfully")


def check_docker_compose():
    """Check if docker-compose.yml needs manual attention"""
    compose_path = "docker-compose.yml"
    
    if not os.path.exists(compose_path):
        print("⚠️  No docker-compose.yml found")
        return
    
    with open(compose_path, 'r') as f:
        content = f.read()
    
    # Check for old hardcoded ports
    hardcoded_ports = ['8080:8000', '3001:80', '8790:80', '11435:11434']
    found_hardcoded = []
    
    for port in hardcoded_ports:
        if port in content and '${' not in content.split(port)[0].split('\n')[-1]:
            found_hardcoded.append(port)
    
    if found_hardcoded:
        print("⚠️  Found potentially hardcoded ports in docker-compose.yml:")
        for port in found_hardcoded:
            print(f"   - {port}")
        print("   Consider updating to use environment variables")
    else:
        print("✅ docker-compose.yml appears to use environment variables")


def main():
    """Main migration function"""
    print("🚀 tagTok Port Configuration Migration")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("docker-compose.yml") and not os.path.exists(".env.example"):
        print("❌ This doesn't appear to be a tagTok project directory")
        print("   Please run this script from the tagTok root directory")
        sys.exit(1)
    
    print("📋 Starting migration process...")
    
    # Migrate .env file
    migrate_env_file()
    
    # Check docker-compose.yml
    check_docker_compose()
    
    print("\n🎉 Migration completed!")
    print("\n📝 Next steps:")
    print("1. Review your .env file to ensure all values are correct")
    print("2. Test your configuration with: docker-compose config")
    print("3. Restart your services: docker-compose down && docker-compose up -d")
    print("\n💡 If you encounter issues, restore from backup files (.backup)")


if __name__ == "__main__":
    main()
