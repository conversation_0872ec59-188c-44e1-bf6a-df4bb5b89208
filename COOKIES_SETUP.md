# Cookie Setup for TikTok Video Downloads

## Problem
You're seeing this error: "This content requires authentication. Please export cookies from your browser to a file and configure YTDLP_COOKIES_FILE environment variable."

This happens because some TikTok videos require you to be logged in to view them. The Docker container can't access your browser's login session, so you need to provide cookies manually.

## Quick Solution

### Step 1: Export TikTok Cookies

**For Chrome:**
1. Install the "Get cookies.txt LOCALLY" extension from Chrome Web Store
2. Go to [TikTok.com](https://tiktok.com) and make sure you're logged in
3. Click the extension icon and click "Export"
4. Save the downloaded file as `cookies.txt`

**For Firefox:**
1. Install the "cookies.txt" extension from Firefox Add-ons
2. Go to [TikTok.com](https://tiktok.com) and make sure you're logged in
3. Click the extension icon and export cookies
4. Save the file as `cookies.txt`

### Step 2: Place Cookies File
1. Copy your `cookies.txt` file to the `data/cookies/` directory in your tagTok project
2. The file should be located at: `data/cookies/cookies.txt`

### Step 3: Verify Configuration
Your `.env` file should already have:
```bash
YTDLP_COOKIES_FILE=/app/cookies/cookies.txt
```

### Step 4: Restart Application
```bash
docker-compose restart backend
```

### Step 5: Test
Try downloading the TikTok video again. It should now work!

## Alternative Method (Browser Cookie Extraction)

If you prefer, you can use browser cookie extraction instead:

1. Edit your `.env` file:
```bash
# Comment out the cookies file line
# YTDLP_COOKIES_FILE=/app/cookies/cookies.txt

# Enable browser cookie extraction
YTDLP_COOKIES_FROM_BROWSER=chrome
```

2. Restart: `docker-compose restart backend`

**Note:** This method may not work in all Docker environments.

## Troubleshooting

### "Could not find chrome cookies database"
This means browser cookie extraction isn't working in your Docker setup. Use the cookies file method instead.

### "Authentication still failing"
1. Make sure you're logged into TikTok in your browser
2. Re-export fresh cookies
3. Verify the cookies file is in the correct location: `data/cookies/cookies.txt`
4. Restart the backend: `docker-compose restart backend`

### "File not found"
Make sure the cookies file path is correct:
- Host system: `./data/cookies/cookies.txt`
- Inside container: `/app/cookies/cookies.txt`

## Security Note

⚠️ **Important:** Your cookies file contains authentication information. Keep it secure and don't share it publicly!
