# 🔧 Port Configuration Guide

This guide explains the new centralized port configuration system in tagTok, which replaces hardcoded ports with flexible environment variables.

## 📋 Overview

The tagTok application now uses **centralized port configuration** through environment variables, making it easy to:
- Change ports without modifying code
- Avoid port conflicts in different environments
- Deploy multiple instances on the same server
- Maintain consistent configuration across services

## 🏗️ Architecture

### Port Types

**External Ports** (Host → Container)
- What users connect to from outside Docker
- Configurable via environment variables
- Can be changed to avoid conflicts

**Internal Ports** (Container → Container)
- Communication between Docker services
- Usually kept as defaults for consistency
- Only change if you know what you're doing

## ⚙️ Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# External ports (what users connect to)
NGINX_PORT=8790                    # Main application access
BACKEND_EXTERNAL_PORT=8080         # Direct backend API access
FRONTEND_EXTERNAL_PORT=3001        # Direct frontend access (dev)
OLLAMA_EXTERNAL_PORT=11435         # Ollama AI service access

# Internal service ports (inside Docker containers)
BACKEND_INTERNAL_PORT=8000         # Backend service port
FRONTEND_INTERNAL_PORT=80          # Frontend service port
OLLAMA_INTERNAL_PORT=11434         # Ollama service port

# Service URLs for application code
BACKEND_URL=http://backend:8000    # Backend service URL
OLLAMA_URL=http://ollama:11434     # Ollama service URL
FRONTEND_URL=http://frontend:80    # Frontend service URL

# API Configuration
REACT_APP_API_URL=https://tagtok.mvt.ar/api  # Frontend API endpoint
```

### Default Ports

| Service | External Port | Internal Port | Purpose |
|---------|---------------|---------------|---------|
| nginx | 8790 | 80 | Main application access |
| backend | 8080 | 8000 | API server |
| frontend | 3001 | 80 | Development frontend |
| ollama | 11435 | 11434 | AI processing |

## 🚀 Usage Examples

### Standard Deployment
```bash
# Use defaults - no changes needed
docker-compose up -d
```

### Custom Ports (Avoid Conflicts)
```bash
# In .env file:
NGINX_PORT=9000
BACKEND_EXTERNAL_PORT=9001
FRONTEND_EXTERNAL_PORT=9002
OLLAMA_EXTERNAL_PORT=9003

# Then deploy:
docker-compose up -d
```

### Multiple Instances
```bash
# Instance 1 (.env)
NGINX_PORT=8790
BACKEND_EXTERNAL_PORT=8080
COMPOSE_PROJECT_NAME=tagtok-prod

# Instance 2 (.env.dev)
NGINX_PORT=8791
BACKEND_EXTERNAL_PORT=8081
COMPOSE_PROJECT_NAME=tagtok-dev

# Deploy both:
docker-compose up -d                    # Instance 1
docker-compose --env-file .env.dev up -d  # Instance 2
```

## 🔄 Migration from Old Configuration

### Automatic Migration

Run the migration script:
```bash
python migrate_port_config.py
```

This will:
- ✅ Backup your existing `.env` file
- ✅ Convert old port variables to new format
- ✅ Add missing configuration keys
- ✅ Preserve your existing settings

### Manual Migration

If you prefer manual migration:

1. **Backup your current configuration:**
   ```bash
   cp .env .env.backup
   cp docker-compose.yml docker-compose.yml.backup
   ```

2. **Update your `.env` file:**
   ```bash
   # Old format:
   BACKEND_PORT=8090
   FRONTEND_PORT=3001
   OLLAMA_PORT=11435

   # New format:
   BACKEND_EXTERNAL_PORT=8090
   FRONTEND_EXTERNAL_PORT=3001
   OLLAMA_EXTERNAL_PORT=11435
   BACKEND_INTERNAL_PORT=8000
   FRONTEND_INTERNAL_PORT=80
   OLLAMA_INTERNAL_PORT=11434
   ```

3. **Test the configuration:**
   ```bash
   docker-compose config  # Validate syntax
   docker-compose down     # Stop services
   docker-compose up -d    # Start with new config
   ```

## 🛠️ Troubleshooting

### Port Conflicts
```bash
# Check what's using a port:
lsof -i :8790
netstat -tulpn | grep 8790

# Change to different port in .env:
NGINX_PORT=8791
```

### Service Communication Issues
```bash
# Check internal service URLs:
docker-compose exec backend python -c "from config import config; config.print_config()"

# Verify service connectivity:
docker-compose exec backend curl http://ollama:11434/api/version
```

### Configuration Validation
```bash
# Validate docker-compose configuration:
docker-compose config

# Check environment variables:
docker-compose exec backend env | grep PORT
docker-compose exec frontend env | grep REACT_APP
```

## 🔍 Advanced Configuration

### Development vs Production

**Development (.env.development):**
```bash
BACKEND_URL=http://localhost:8080
OLLAMA_URL=http://localhost:11435
REACT_APP_API_URL=http://localhost:8080
```

**Production (.env.production):**
```bash
BACKEND_URL=http://backend:8000
OLLAMA_URL=http://ollama:11434
REACT_APP_API_URL=https://yourdomain.com/api
```

### Load Balancer Integration

For production with load balancers:
```bash
# Internal services only (no external ports)
NGINX_PORT=80
BACKEND_EXTERNAL_PORT=8000
FRONTEND_EXTERNAL_PORT=3000

# Load balancer handles external access
```

## 📚 Reference

### Configuration Files

- **`.env`** - Main environment configuration
- **`backend/config.py`** - Backend configuration management
- **`frontend/src/config/index.ts`** - Frontend configuration management
- **`docker-compose.yml`** - Service orchestration with env vars

### Environment Variable Precedence

1. **Docker Compose environment section** (highest priority)
2. **`.env` file**
3. **System environment variables**
4. **Default values in code** (lowest priority)

### Backward Compatibility

The new system maintains backward compatibility:
- Old hardcoded ports still work as fallbacks
- Existing deployments continue to function
- Migration is optional but recommended

## 🆘 Support

If you encounter issues:

1. **Check logs:**
   ```bash
   docker-compose logs backend
   docker-compose logs frontend
   ```

2. **Validate configuration:**
   ```bash
   docker-compose config
   ```

3. **Reset to defaults:**
   ```bash
   cp .env.example .env
   docker-compose down
   docker-compose up -d --build
   ```

4. **Restore from backup:**
   ```bash
   cp .env.backup .env
   docker-compose down
   docker-compose up -d
   ```
