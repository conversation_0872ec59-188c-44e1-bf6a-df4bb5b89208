#!/usr/bin/env python3

import requests
import json

def test_ollama_directly():
    """Test Ollama directly to see the full response"""
    
    # Get the transcript from video 59
    video_response = requests.get("http://localhost:8090/videos/59")
    if video_response.status_code != 200:
        print("Failed to get video 59")
        return
    
    video_data = video_response.json()
    transcript = video_data.get('transcript', '')
    title = video_data.get('title', '')
    
    print(f"Video Title: {title}")
    print(f"Transcript length: {len(transcript)} characters")
    print(f"Transcript preview: {transcript[:200]}...")
    print("\n" + "="*50 + "\n")
    
    # Test the Ollama endpoint directly with a shorter prompt
    prompt = f'''Analiza esta transcripción y extrae una receta en JSON:

TRANSCRIPCIÓN: "{transcript[:800]}"

Responde SOLO con JSON válido:
{{
  "title": "nombre de la receta",
  "description": "descripción breve",
  "ingredients": [
    {{"name": "ingrediente", "amount": "cantidad", "unit": "unidad", "notes": "notas"}}
  ],
  "instructions": [
    {{"step_number": 1, "instruction": "paso", "time": "tiempo", "temperature": "temperatura"}}
  ],
  "prep_time": "15 minutos",
  "cook_time": "20 minutos", 
  "total_time": "35 minutos",
  "servings": "4 porciones",
  "difficulty": "Fácil",
  "cuisine_type": "Italiana",
  "confidence": 0.8
}}'''
    
    ollama_payload = {
        "model": "llama3.2:3b",
        "prompt": prompt,
        "stream": False,
        "options": {
            "temperature": 0.1,
            "num_predict": 1500
        }
    }
    
    try:
        print("Sending request to Ollama...")
        response = requests.post(
            "http://localhost:11435/api/generate",
            json=ollama_payload,
            timeout=60
        )
        
        if response.status_code == 200:
            ollama_response = response.json()
            recipe_text = ollama_response.get('response', '')
            
            print("Raw Ollama Response:")
            print("-" * 50)
            print(recipe_text)
            print("-" * 50)
            
            # Try to parse as JSON
            try:
                recipe_json = json.loads(recipe_text)
                print("\n✅ JSON is valid!")
                print(f"Recipe title: {recipe_json.get('title', 'N/A')}")
                print(f"Confidence: {recipe_json.get('confidence', 'N/A')}")
                
            except json.JSONDecodeError as e:
                print(f"\n❌ JSON parsing failed: {e}")
                
                # Try to extract JSON from the response
                start_idx = recipe_text.find('{')
                end_idx = recipe_text.rfind('}') + 1
                
                if start_idx >= 0 and end_idx > start_idx:
                    json_part = recipe_text[start_idx:end_idx]
                    print(f"Extracted JSON part:")
                    print(json_part)
                    
                    try:
                        recipe_json = json.loads(json_part)
                        print("✅ Fixed JSON is valid!")
                        print(f"Recipe title: {recipe_json.get('title', 'N/A')}")
                    except json.JSONDecodeError as e2:
                        print(f"❌ Still invalid JSON: {e2}")
                
        else:
            print(f"Ollama request failed: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"Error testing Ollama: {e}")

if __name__ == "__main__":
    test_ollama_directly()
