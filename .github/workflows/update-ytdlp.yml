name: Update yt-dlp

on:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * 1'
  workflow_dispatch: # Allow manual trigger

permissions:
  contents: write
  pull-requests: write

jobs:
  check-ytdlp-update:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install requests

    - name: Check for yt-dlp updates
      id: check-update
      run: |
        # Get current version from requirements.txt
        CURRENT_VERSION=$(grep "yt-dlp>=" backend/requirements.txt | sed 's/yt-dlp>=//')
        echo "Current version: $CURRENT_VERSION"
        echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
        
        # Get latest version from PyPI
        LATEST_VERSION=$(python -c "
        import requests
        response = requests.get('https://pypi.org/pypi/yt-dlp/json')
        data = response.json()
        print(data['info']['version'])
        ")
        echo "Latest version: $LATEST_VERSION"
        
        # Compare versions
        if [ "$CURRENT_VERSION" != "$LATEST_VERSION" ]; then
          echo "update_available=true" >> $GITHUB_OUTPUT
          echo "new_version=$LATEST_VERSION" >> $GITHUB_OUTPUT
          echo "Update available: $CURRENT_VERSION -> $LATEST_VERSION"
        else
          echo "update_available=false" >> $GITHUB_OUTPUT
          echo "No update available"
        fi

    - name: Update requirements.txt
      if: steps.check-update.outputs.update_available == 'true'
      run: |
        # Update yt-dlp version in requirements.txt
        sed -i "s/yt-dlp>=.*/yt-dlp>=${{ steps.check-update.outputs.new_version }}/" backend/requirements.txt

    - name: Create Pull Request
      if: steps.check-update.outputs.update_available == 'true'
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: "chore: update yt-dlp to ${{ steps.check-update.outputs.new_version }}"
        committer: GitHub Action <<EMAIL>>
        author: GitHub Action <<EMAIL>>
        branch: auto-update-ytdlp-${{ steps.check-update.outputs.new_version }}
        branch-suffix: timestamp
        base: main
        title: "🤖 Auto-update yt-dlp to ${{ steps.check-update.outputs.new_version }}"
        body: |
          ## 🤖 Automated yt-dlp Update

          This PR updates yt-dlp from ${{ steps.check-update.outputs.current_version }} to ${{ steps.check-update.outputs.new_version }}.

          ### Changes:
          - Updated yt-dlp version in `backend/requirements.txt`

          ### Testing Checklist:
          - [ ] Verify downloads still work correctly
          - [ ] Test with various video platforms (YouTube, etc.)
          - [ ] Check for any breaking changes
          - [ ] Verify backend functionality

          ### Deployment:
          After merging, the updated version will be deployed with your next deployment.

          ---
          *This PR targets the `main` branch for direct deployment.*
        labels: |
          dependencies
          automated
        reviewers: |
          axelmvt
        draft: false
        delete-branch: true
      id: create-pr

    - name: Send Slack notification
      if: steps.check-update.outputs.update_available == 'true' && steps.create-pr.outputs.pull-request-number
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        custom_payload: |
          {
            "text": "🤖 *yt-dlp Update Available for TagTok!*",
            "blocks": [
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "🤖 *yt-dlp Update Available for TagTok!*\n\n📦 *Current Version:* `${{ steps.check-update.outputs.current_version }}`\n🆕 *New Version:* `${{ steps.check-update.outputs.new_version }}`\n🔄 *Pull Request #${{ steps.create-pr.outputs.pull-request-number }}:* Ready for review!"
                }
              },
              {
                "type": "actions",
                "elements": [
                  {
                    "type": "button",
                    "text": {
                      "type": "plain_text",
                      "text": "📝 Review Pull Request"
                    },
                    "url": "${{ steps.create-pr.outputs.pull-request-url }}",
                    "style": "primary"
                  },
                  {
                    "type": "button",
                    "text": {
                      "type": "plain_text",
                      "text": "👀 View Repository"
                    },
                    "url": "https://github.com/${{ github.repository }}"
                  }
                ]
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}