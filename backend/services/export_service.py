from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from models.database import Video, Tag
from services.analytics_service import AnalyticsService

class ExportService:
    def __init__(self, db: Session):
        self.db = db
        self.analytics_service = AnalyticsService(db)
    
    def export_videos(
        self,
        include_transcript: bool = True,
        include_tags: bool = True,
        tag_filter: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Export video data to dictionary format"""
        query = self.db.query(Video)
        
        # Apply tag filter if specified
        if tag_filter:
            query = query.join(Video.tags).filter(Tag.name.in_(tag_filter))
        
        videos = query.order_by(Video.upload_date.desc()).all()
        
        export_data = []
        for video in videos:
            video_data = {
                "id": video.id,
                "filename": video.filename,
                "original_filename": video.original_filename,
                "title": video.title,
                "file_size_mb": round(video.file_size / (1024 * 1024), 2) if video.file_size else 0,
                "duration_seconds": video.duration,
                "duration_formatted": self._format_duration(video.duration) if video.duration else "N/A",
                "width": video.width,
                "height": video.height,
                "fps": video.fps,
                "upload_date": video.upload_date.isoformat() if video.upload_date else None,
                "processed": video.processed,
                "processing_status": video.processing_status,
                "transcript_language": video.transcript_language
            }
            
            # Add transcript if requested
            if include_transcript:
                video_data["transcript"] = video.transcript
            
            # Add tags if requested
            if include_tags:
                video_data["tags"] = [
                    {
                        "name": tag.name,
                        "color": tag.color,
                        "description": tag.description
                    }
                    for tag in video.tags
                ]
                video_data["tag_names"] = [tag.name for tag in video.tags]
                video_data["tag_count"] = len(video.tags)
            
            export_data.append(video_data)
        
        return export_data
    
    def export_tags(self) -> List[Dict[str, Any]]:
        """Export tags data to dictionary format"""
        tags = self.db.query(Tag).order_by(Tag.usage_count.desc()).all()
        
        export_data = []
        for tag in tags:
            tag_data = {
                "id": tag.id,
                "name": tag.name,
                "color": tag.color,
                "description": tag.description,
                "usage_count": tag.usage_count,
                "created_date": tag.created_date.isoformat() if tag.created_date else None
            }
            export_data.append(tag_data)
        
        return export_data
    
    def export_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Export analytics data"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        analytics_data = {
            "export_date": datetime.now().isoformat(),
            "period_days": days,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "summary": self.analytics_service.get_summary(),
            "top_tags": self.analytics_service.get_top_tags_data(20),
            "language_distribution": self.analytics_service.get_language_distribution(),
            "duration_distribution": self.analytics_service.get_duration_distribution(),
            "processing_status": self.analytics_service.get_processing_status(),
            "upload_timeline": self.analytics_service.get_upload_timeline(start_date, end_date),
            "monthly_stats": self.analytics_service.get_monthly_stats(12),
            "tag_usage_trends": self.analytics_service.get_tag_usage_trends(days)
        }
        
        return analytics_data
    
    def export_video_metadata_csv_format(
        self,
        include_transcript: bool = True,
        include_tags: bool = True,
        tag_filter: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Export video data in CSV-friendly format (flattened)"""
        videos_data = self.export_videos(include_transcript, include_tags, tag_filter)
        
        csv_data = []
        for video in videos_data:
            # Flatten the data for CSV
            csv_row = {
                "id": video["id"],
                "filename": video["filename"],
                "original_filename": video["original_filename"],
                "title": video["title"],
                "file_size_mb": video["file_size_mb"],
                "duration_seconds": video["duration_seconds"],
                "duration_formatted": video["duration_formatted"],
                "width": video["width"],
                "height": video["height"],
                "fps": video["fps"],
                "upload_date": video["upload_date"],
                "processed": video["processed"],
                "processing_status": video["processing_status"],
                "transcript_language": video["transcript_language"]
            }
            
            if include_transcript:
                # Clean transcript for CSV (remove newlines, limit length)
                transcript = video.get("transcript", "")
                if transcript:
                    transcript = transcript.replace("\n", " ").replace("\r", " ")
                    if len(transcript) > 1000:  # Limit transcript length in CSV
                        transcript = transcript[:1000] + "..."
                csv_row["transcript"] = transcript
            
            if include_tags:
                csv_row["tag_names"] = "; ".join(video.get("tag_names", []))
                csv_row["tag_count"] = video.get("tag_count", 0)
                
                # Add individual tag details (up to 5 tags to keep CSV manageable)
                tags = video.get("tags", [])[:5]
                for i, tag in enumerate(tags, 1):
                    csv_row[f"tag_{i}_name"] = tag["name"]
                    csv_row[f"tag_{i}_color"] = tag["color"]
                    csv_row[f"tag_{i}_description"] = tag["description"]
                
                # Fill empty tag columns
                for i in range(len(tags) + 1, 6):
                    csv_row[f"tag_{i}_name"] = ""
                    csv_row[f"tag_{i}_color"] = ""
                    csv_row[f"tag_{i}_description"] = ""
            
            csv_data.append(csv_row)
        
        return csv_data
    
    def _format_duration(self, duration_seconds: float) -> str:
        """Format duration in seconds to human-readable format"""
        if not duration_seconds:
            return "0:00"
        
        hours = int(duration_seconds // 3600)
        minutes = int((duration_seconds % 3600) // 60)
        seconds = int(duration_seconds % 60)
        
        if hours > 0:
            return f"{hours}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes}:{seconds:02d}"
    
    def get_export_summary(self) -> Dict[str, Any]:
        """Get summary of exportable data"""
        total_videos = self.db.query(Video).count()
        total_tags = self.db.query(Tag).count()
        processed_videos = self.db.query(Video).filter(Video.processed == True).count()
        
        return {
            "total_videos": total_videos,
            "total_tags": total_tags,
            "processed_videos": processed_videos,
            "videos_with_transcripts": self.db.query(Video).filter(
                Video.transcript.isnot(None)
            ).count(),
            "videos_with_tags": self.db.query(Video).join(Video.tags).count(),
            "export_formats_available": ["csv", "json"],
            "last_updated": datetime.now().isoformat()
        }
