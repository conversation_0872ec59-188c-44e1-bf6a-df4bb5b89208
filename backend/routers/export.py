from fastapi import APIRouter, Depends, Query, HTTPException
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import Optional
import csv
import json
import io
from datetime import datetime

from models.database import get_db
from models.schemas import ExportFormat
from services.export_service import ExportService

router = APIRouter()

@router.get("/videos")
async def export_videos(
    format: str = Query("csv", regex="^(csv|json)$"),
    include_transcript: bool = Query(True),
    include_tags: bool = Query(True),
    tag_filter: Optional[str] = Query(None, description="Comma-separated tag names"),
    db: Session = Depends(get_db)
):
    """Export video metadata in CSV or JSON format"""
    export_service = ExportService(db)
    
    # Parse tag filter
    tag_list = tag_filter.split(",") if tag_filter else None
    
    # Get export data
    data = export_service.export_videos(
        include_transcript=include_transcript,
        include_tags=include_tags,
        tag_filter=tag_list
    )
    
    if format == "csv":
        # Generate CSV
        output = io.StringIO()
        if data:
            writer = csv.DictWriter(output, fieldnames=data[0].keys())
            writer.writeheader()
            writer.writerows(data)
        
        # Create response
        response = StreamingResponse(
            io.BytesIO(output.getvalue().encode('utf-8')),
            media_type="text/csv",
            headers={
                "Content-Disposition": f"attachment; filename=videos_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            }
        )
        return response
    
    elif format == "json":
        # Generate JSON
        json_data = json.dumps(data, indent=2, default=str)
        
        # Create response
        response = StreamingResponse(
            io.BytesIO(json_data.encode('utf-8')),
            media_type="application/json",
            headers={
                "Content-Disposition": f"attachment; filename=videos_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            }
        )
        return response

@router.get("/tags")
async def export_tags(
    format: str = Query("csv", regex="^(csv|json)$"),
    db: Session = Depends(get_db)
):
    """Export tags data in CSV or JSON format"""
    export_service = ExportService(db)
    
    # Get export data
    data = export_service.export_tags()
    
    if format == "csv":
        # Generate CSV
        output = io.StringIO()
        if data:
            writer = csv.DictWriter(output, fieldnames=data[0].keys())
            writer.writeheader()
            writer.writerows(data)
        
        # Create response
        response = StreamingResponse(
            io.BytesIO(output.getvalue().encode('utf-8')),
            media_type="text/csv",
            headers={
                "Content-Disposition": f"attachment; filename=tags_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            }
        )
        return response
    
    elif format == "json":
        # Generate JSON
        json_data = json.dumps(data, indent=2, default=str)
        
        # Create response
        response = StreamingResponse(
            io.BytesIO(json_data.encode('utf-8')),
            media_type="application/json",
            headers={
                "Content-Disposition": f"attachment; filename=tags_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            }
        )
        return response

@router.get("/analytics")
async def export_analytics(
    format: str = Query("json", regex="^(csv|json)$"),
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """Export analytics data"""
    export_service = ExportService(db)
    
    # Get analytics data
    data = export_service.export_analytics(days)
    
    if format == "json":
        # Generate JSON
        json_data = json.dumps(data, indent=2, default=str)
        
        # Create response
        response = StreamingResponse(
            io.BytesIO(json_data.encode('utf-8')),
            media_type="application/json",
            headers={
                "Content-Disposition": f"attachment; filename=analytics_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            }
        )
        return response
    
    else:
        raise HTTPException(
            status_code=400,
            detail="Analytics export only supports JSON format"
        )
