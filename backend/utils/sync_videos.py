#!/usr/bin/env python3
"""
Video Database Sync Utility

This script synchronizes the video database with the actual files in the videos directory.
It handles:
1. Adding missing videos to the database
2. Removing database entries for deleted files
3. Updating file metadata
4. Generating missing thumbnails
"""

import os
import sys
import asyncio
from pathlib import Path
from datetime import datetime
from typing import List, Tuple, Optional
import uuid

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from models.database import get_db, Video, create_tables
from services.video_service import VideoService
from services.processing_service import ProcessingService
from utils.video_utils import get_video_metadata


class VideoSyncService:
    def __init__(self, videos_dir: str = "videos"):
        self.videos_dir = Path(videos_dir)
        self.db_session = next(get_db())
        self.video_service = VideoService(self.db_session)
        self.processing_service = ProcessingService(self.db_session)
        
    def get_filesystem_videos(self) -> List[Path]:
        """Get all video files from the filesystem"""
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv'}
        video_files = []
        
        for ext in video_extensions:
            video_files.extend(self.videos_dir.glob(f'*{ext}'))
            
        return sorted(video_files)
    
    def get_database_videos(self) -> List[Video]:
        """Get all videos from the database"""
        return self.db_session.query(Video).all()
    
    def find_missing_in_database(self) -> List[Path]:
        """Find video files that exist in filesystem but not in database"""
        fs_videos = {f.name for f in self.get_filesystem_videos()}
        db_videos = {v.filename for v in self.get_database_videos()}
        
        missing_filenames = fs_videos - db_videos
        return [self.videos_dir / filename for filename in missing_filenames]
    
    def find_orphaned_in_database(self) -> List[Video]:
        """Find database entries for files that no longer exist"""
        fs_videos = {f.name for f in self.get_filesystem_videos()}
        db_videos = self.get_database_videos()
        
        orphaned = []
        for video in db_videos:
            if video.filename not in fs_videos:
                orphaned.append(video)
                
        return orphaned
    
    async def add_missing_video(self, video_path: Path) -> Optional[Video]:
        """Add a missing video file to the database"""
        try:
            print(f"Adding missing video: {video_path.name}")
            
            # Get video metadata
            metadata = get_video_metadata(str(video_path))
            if not metadata:
                print(f"  ❌ Could not get metadata for {video_path.name}")
                return None
            
            # Create video record
            video = Video(
                filename=video_path.name,
                original_filename=video_path.name,
                title=video_path.stem,  # Use filename without extension as title
                file_path=str(video_path),
                file_size=video_path.stat().st_size,
                duration=metadata.get('duration'),
                width=metadata.get('width'),
                height=metadata.get('height'),
                fps=metadata.get('fps'),
                upload_date=datetime.fromtimestamp(video_path.stat().st_mtime),
                processed=False,
                processing_status="pending"
            )
            
            self.db_session.add(video)
            self.db_session.commit()
            self.db_session.refresh(video)
            
            print(f"  ✅ Added video {video.id}: {video.filename}")
            
            # Queue for processing (thumbnail generation, transcription, etc.)
            await self.processing_service.process_video_async(video.id)
            print(f"  🔄 Queued for processing: {video.filename}")
            
            return video
            
        except Exception as e:
            print(f"  ❌ Error adding {video_path.name}: {e}")
            self.db_session.rollback()
            return None
    
    def remove_orphaned_video(self, video: Video) -> bool:
        """Remove orphaned database entry"""
        try:
            print(f"Removing orphaned database entry: {video.filename}")
            self.db_session.delete(video)
            self.db_session.commit()
            print(f"  ✅ Removed orphaned entry: {video.filename}")
            return True
        except Exception as e:
            print(f"  ❌ Error removing {video.filename}: {e}")
            self.db_session.rollback()
            return False
    
    async def sync_videos(self, dry_run: bool = False) -> Tuple[int, int]:
        """
        Synchronize videos between filesystem and database
        
        Args:
            dry_run: If True, only report what would be done without making changes
            
        Returns:
            Tuple of (added_count, removed_count)
        """
        print("🔄 Starting video database synchronization...")
        print(f"📁 Videos directory: {self.videos_dir}")
        
        # Find discrepancies
        missing_in_db = self.find_missing_in_database()
        orphaned_in_db = self.find_orphaned_in_database()
        
        print(f"\n📊 Sync Analysis:")
        print(f"  • Files in filesystem: {len(self.get_filesystem_videos())}")
        print(f"  • Records in database: {len(self.get_database_videos())}")
        print(f"  • Missing from database: {len(missing_in_db)}")
        print(f"  • Orphaned in database: {len(orphaned_in_db)}")
        
        if dry_run:
            print(f"\n🔍 DRY RUN - No changes will be made")
            if missing_in_db:
                print(f"\nWould add to database:")
                for video_path in missing_in_db:
                    print(f"  + {video_path.name}")
            
            if orphaned_in_db:
                print(f"\nWould remove from database:")
                for video in orphaned_in_db:
                    print(f"  - {video.filename}")
            
            return 0, 0
        
        added_count = 0
        removed_count = 0
        
        # Add missing videos
        if missing_in_db:
            print(f"\n➕ Adding {len(missing_in_db)} missing videos to database...")
            for video_path in missing_in_db:
                video = await self.add_missing_video(video_path)
                if video:
                    added_count += 1
        
        # Remove orphaned entries
        if orphaned_in_db:
            print(f"\n🗑️  Removing {len(orphaned_in_db)} orphaned database entries...")
            for video in orphaned_in_db:
                if self.remove_orphaned_video(video):
                    removed_count += 1
        
        print(f"\n✅ Synchronization complete!")
        print(f"  • Added: {added_count} videos")
        print(f"  • Removed: {removed_count} orphaned entries")
        
        return added_count, removed_count
    
    def close(self):
        """Close database session"""
        self.db_session.close()


async def main():
    """Main function for command-line usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Sync video database with filesystem")
    parser.add_argument("--dry-run", action="store_true", 
                       help="Show what would be done without making changes")
    parser.add_argument("--videos-dir", default="videos",
                       help="Path to videos directory (default: videos)")
    
    args = parser.parse_args()
    
    # Ensure database tables exist
    create_tables()
    
    # Create sync service
    sync_service = VideoSyncService(args.videos_dir)
    
    try:
        # Run synchronization
        added, removed = await sync_service.sync_videos(dry_run=args.dry_run)
        
        if not args.dry_run:
            print(f"\n📈 Final counts:")
            print(f"  • Total videos in filesystem: {len(sync_service.get_filesystem_videos())}")
            print(f"  • Total videos in database: {len(sync_service.get_database_videos())}")
            
    except Exception as e:
        print(f"❌ Error during synchronization: {e}")
        return 1
    finally:
        sync_service.close()
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
