import asyncio
import json
import re
import requests
from typing import Dict, List, Optional, Tuple
from models.schemas import RecipeCreate, IngredientBase, InstructionStep
from config import config


class RecipeExtractor:
    """Extract recipe information from video transcripts using AI"""

    def __init__(self):
        # Use centralized configuration for Ollama endpoints
        self.ollama_endpoints = config.get_ollama_endpoints()
    
    async def extract_recipe(self, transcript: str, video_title: str = "", video_id: int = None) -> Optional[RecipeCreate]:
        """Extract recipe from transcript if it's a cooking video"""
        try:
            # First, check if this is actually a recipe/cooking video
            if not self._is_cooking_video(transcript, video_title):
                return None
            
            # Extract recipe using Ollama
            recipe_data = await self._extract_recipe_with_ollama(transcript, video_title)
            
            if recipe_data and video_id:
                # Convert to RecipeCreate schema
                return self._convert_to_recipe_create(recipe_data, video_id)
            
            return None
            
        except Exception as e:
            print(f"Recipe extraction failed: {e}")
            return None
    
    def _is_cooking_video(self, transcript: str, title: str = "") -> bool:
        """Determine if this is a cooking/recipe video"""
        text = f"{title} {transcript}".lower()
        
        # Cooking keywords in multiple languages
        cooking_keywords = [
            # English
            'recipe', 'cook', 'bake', 'fry', 'boil', 'ingredients', 'kitchen', 'oven',
            'stir', 'mix', 'chop', 'slice', 'dice', 'season', 'taste', 'serve',
            'tablespoon', 'teaspoon', 'cup', 'pound', 'ounce', 'gram', 'liter',
            'minutes', 'degrees', 'heat', 'oil', 'salt', 'pepper', 'garlic',
            
            # Spanish
            'receta', 'cocinar', 'hornear', 'freír', 'hervir', 'ingredientes', 'cocina', 'horno',
            'mezclar', 'revolver', 'picar', 'cortar', 'sazonar', 'probar', 'servir',
            'cucharada', 'cucharadita', 'taza', 'gramo', 'litro', 'minutos', 'grados',
            'aceite', 'sal', 'pimienta', 'ajo', 'cebolla', 'tomate', 'pollo', 'carne',
            'pasta', 'arroz', 'verduras', 'queso', 'huevo', 'leche', 'azúcar', 'harina'
        ]
        
        # Count cooking-related words
        cooking_word_count = sum(1 for keyword in cooking_keywords if keyword in text)
        
        # Consider it a cooking video if it has enough cooking keywords
        return cooking_word_count >= 3
    
    async def _extract_recipe_with_ollama(self, transcript: str, title: str = "") -> Optional[Dict]:
        """Extract recipe using Ollama LLM"""
        try:
            # Run in executor to avoid blocking the event loop
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._extract_recipe_sync,
                transcript,
                title
            )
            return result
        except Exception as e:
            print(f"Recipe extraction with Ollama failed: {e}")
            return None

    def _extract_recipe_sync(self, transcript: str, title: str = "") -> Optional[Dict]:
        """Synchronous recipe extraction method"""
        try:
            # Truncate transcript if too long to fit in context
            max_transcript_length = 800
            truncated_transcript = transcript[:max_transcript_length] if len(transcript) > max_transcript_length else transcript

            prompt = f'''Extrae receta de esta transcripción en JSON:

TRANSCRIPCIÓN: "{truncated_transcript}"

Responde SOLO JSON válido en español:
{{
  "title": "nombre receta",
  "description": "descripción breve",
  "ingredients": [{{"name": "ingrediente", "amount": "cantidad", "unit": "unidad", "notes": "notas"}}],
  "instructions": [{{"step_number": 1, "instruction": "paso", "time": "tiempo", "temperature": "temp"}}],
  "prep_time": "15 min",
  "cook_time": "20 min",
  "total_time": "35 min",
  "servings": "4 porciones",
  "difficulty": "Fácil",
  "cuisine_type": "tipo",
  "confidence": 0.8
}}

Si no hay receta: {{"confidence": 0.0}}'''

            print(f"Extracting recipe from transcript: {transcript[:100]}...")

            # Try Ollama endpoints
            for endpoint in self.ollama_endpoints:
                try:
                    print(f"Trying Ollama endpoint: {endpoint}")
                    response = requests.post(
                        endpoint,
                        json={
                            'model': 'llama3.2:3b',
                            'prompt': prompt,
                            'stream': False,
                            'options': {
                                'temperature': 0.3,  # Low temperature for consistent extraction
                                'top_p': 0.9,
                                'num_predict': 4000,  # Increased limit for complete recipes
                                'stop': ['\n\nHuman:', '\n\nAssistant:', 'JSON:']
                            }
                        },
                        timeout=45  # Reduced timeout for recipe extraction
                    )

                    if response.status_code == 200:
                        print(f"Successfully connected to Ollama at {endpoint}")
                        result = response.json()
                        generated_text = result.get('response', '').strip()
                        print(f"Ollama recipe response: {generated_text[:200]}...")

                        # Parse the JSON response
                        recipe_data = self._parse_recipe_response(generated_text)

                        if recipe_data and recipe_data.get('confidence', 0) > 0.5:
                            print(f"Recipe extracted with confidence: {recipe_data.get('confidence')}")
                            return recipe_data
                        else:
                            print("Low confidence recipe extraction, skipping")
                            return None

                except requests.exceptions.RequestException as e:
                    print(f"Failed to connect to {endpoint}: {e}")
                    continue

            print("All Ollama endpoints failed")
            return None

        except Exception as e:
            print(f"Recipe extraction with Ollama failed: {e}")
            return None
    
    def _parse_recipe_response(self, response_text: str) -> Optional[Dict]:
        """Parse Ollama response and extract recipe JSON"""
        try:
            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()

                # Try to parse as-is first
                try:
                    recipe_data = json.loads(json_str)
                    return recipe_data
                except json.JSONDecodeError as e:
                    # If parsing fails, try to fix common issues
                    print(f"JSON parsing failed: {e}")
                    print("Attempting to fix truncated JSON...")

                    # Try to complete the JSON if it's truncated
                    fixed_json = self._fix_truncated_json(json_str)
                    if fixed_json:
                        try:
                            recipe_data = json.loads(fixed_json)
                            print("Successfully fixed truncated JSON")
                            return recipe_data
                        except json.JSONDecodeError as e2:
                            print(f"Could not fix JSON: {e2}")

                    # If JSON fixing fails, try to extract basic info manually
                    print("Attempting manual extraction from partial response...")
                    return self._extract_basic_recipe_info(response_text)
            else:
                print("No JSON found in recipe response")
                return None

        except Exception as e:
            print(f"Failed to parse recipe JSON: {e}")
            return None

    def _fix_truncated_json(self, json_str: str) -> Optional[str]:
        """Attempt to fix truncated JSON by closing incomplete structures"""
        try:
            # Remove any trailing incomplete content
            lines = json_str.split('\n')
            fixed_lines = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Skip lines that look incomplete (no closing quote, etc.)
                if line.endswith('"') and not line.endswith('",') and not line.endswith('"}'):
                    # Incomplete string value, skip
                    break

                fixed_lines.append(line)

            # Reconstruct JSON
            json_str = '\n'.join(fixed_lines)

            # Ensure proper closing
            if json_str.count('[') > json_str.count(']'):
                json_str += ']'
            if json_str.count('{') > json_str.count('}'):
                json_str += '}'

            # Remove trailing commas before closing braces/brackets
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

            return json_str

        except Exception as e:
            print(f"Error fixing JSON: {e}")
            return None

    def _extract_basic_recipe_info(self, response_text: str) -> Optional[Dict]:
        """Extract basic recipe info from partial response when JSON parsing fails"""
        try:
            # Look for title
            title_match = re.search(r'"title":\s*"([^"]+)"', response_text)
            title = title_match.group(1) if title_match else "Receta extraída"

            # Look for description
            desc_match = re.search(r'"description":\s*"([^"]+)"', response_text)
            description = desc_match.group(1) if desc_match else "Receta de cocina"

            # Return basic recipe with low confidence
            return {
                "title": title,
                "description": description,
                "ingredients": [],
                "instructions": [],
                "confidence": 0.3  # Low confidence for partial extraction
            }

        except Exception as e:
            print(f"Error in manual extraction: {e}")
            return None
    
    def _convert_to_recipe_create(self, recipe_data: Dict, video_id: int) -> RecipeCreate:
        """Convert extracted recipe data to RecipeCreate schema"""
        try:
            # Convert ingredients
            ingredients = []
            for ing_data in recipe_data.get('ingredients', []):
                ingredient = IngredientBase(
                    name=ing_data.get('name', ''),
                    amount=ing_data.get('amount'),
                    unit=ing_data.get('unit'),
                    notes=ing_data.get('notes')
                )
                ingredients.append(ingredient)
            
            # Convert instructions
            instructions = []
            for inst_data in recipe_data.get('instructions', []):
                instruction = InstructionStep(
                    step_number=inst_data.get('step_number', len(instructions) + 1),
                    instruction=inst_data.get('instruction', ''),
                    time=inst_data.get('time'),
                    temperature=inst_data.get('temperature')
                )
                instructions.append(instruction)
            
            # Create RecipeCreate object
            recipe_create = RecipeCreate(
                video_id=video_id,
                title=recipe_data.get('title'),
                description=recipe_data.get('description'),
                ingredients=ingredients,
                instructions=instructions,
                prep_time=recipe_data.get('prep_time'),
                cook_time=recipe_data.get('cook_time'),
                total_time=recipe_data.get('total_time'),
                servings=recipe_data.get('servings'),
                difficulty=recipe_data.get('difficulty'),
                cuisine_type=recipe_data.get('cuisine_type'),
                extraction_confidence=recipe_data.get('confidence', 0.0)
            )
            
            return recipe_create
            
        except Exception as e:
            print(f"Failed to convert recipe data: {e}")
            return None
