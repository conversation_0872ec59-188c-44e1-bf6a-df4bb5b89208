#!/usr/bin/env python3
"""
Batch recipe extraction script for existing videos
This script will scan all videos with transcripts and extract recipes from cooking videos
"""

import os
import sys
import asyncio
from typing import List

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import SessionLocal, Video, Recipe
from services.recipe_service import RecipeService
from utils.recipe_extractor import RecipeExtractor


async def extract_recipes_from_existing_videos():
    """Extract recipes from all existing videos that have transcripts"""
    print("🔄 Starting batch recipe extraction from existing videos...")
    
    db = SessionLocal()
    recipe_service = RecipeService(db)
    recipe_extractor = RecipeExtractor()
    
    try:
        # Get all videos with transcripts that don't have recipes yet
        videos_without_recipes = db.query(Video).filter(
            Video.transcript.isnot(None),
            Video.transcript != "",
            ~Video.id.in_(
                db.query(Recipe.video_id)
            )
        ).all()
        
        print(f"📊 Found {len(videos_without_recipes)} videos with transcripts but no recipes")
        
        if not videos_without_recipes:
            print("✅ All videos with transcripts already have been processed for recipes")
            return
        
        extracted_count = 0
        skipped_count = 0
        
        for i, video in enumerate(videos_without_recipes, 1):
            print(f"\n🎬 Processing video {i}/{len(videos_without_recipes)}: {video.title or video.original_filename}")
            
            try:
                # Extract recipe
                recipe_create = await recipe_extractor.extract_recipe(
                    video.transcript,
                    video.title or video.original_filename,
                    video.id
                )
                
                if recipe_create:
                    # Save recipe
                    recipe = recipe_service.create_recipe(recipe_create)
                    extracted_count += 1
                    print(f"   ✅ Recipe extracted with confidence: {recipe_create.extraction_confidence:.2f}")
                    print(f"   📝 Title: {recipe_create.title}")
                    print(f"   🥘 Ingredients: {len(recipe_create.ingredients)}")
                    print(f"   📋 Steps: {len(recipe_create.instructions)}")
                else:
                    skipped_count += 1
                    print(f"   ⏭️  Not a cooking video, skipped")
                
            except Exception as e:
                print(f"   ❌ Error processing video {video.id}: {e}")
                skipped_count += 1
                continue
        
        print(f"\n🎉 Batch recipe extraction completed!")
        print(f"   ✅ Recipes extracted: {extracted_count}")
        print(f"   ⏭️  Videos skipped: {skipped_count}")
        print(f"   📊 Total processed: {len(videos_without_recipes)}")
        
        if extracted_count > 0:
            print(f"\n📋 Recipe API endpoints now available:")
            print(f"   • GET /api/recipes/ - List all recipes")
            print(f"   • GET /api/recipes/video/{{video_id}} - Get recipe for specific video")
            print(f"   • GET /api/recipes/search/ingredients?ingredients=chicken,rice - Search by ingredients")
        
    except Exception as e:
        print(f"❌ Batch extraction failed: {e}")
        raise
    finally:
        db.close()


async def extract_recipe_for_specific_video(video_id: int):
    """Extract recipe for a specific video ID"""
    print(f"🔄 Extracting recipe for video ID: {video_id}")
    
    db = SessionLocal()
    recipe_service = RecipeService(db)
    recipe_extractor = RecipeExtractor()
    
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            print(f"❌ Video {video_id} not found")
            return
        
        if not video.transcript:
            print(f"❌ Video {video_id} has no transcript")
            return
        
        # Check if recipe already exists
        existing_recipe = recipe_service.get_recipe_by_video_id(video_id)
        if existing_recipe:
            print(f"✅ Recipe already exists for video {video_id}")
            return
        
        print(f"🎬 Video: {video.title or video.original_filename}")
        
        # Extract recipe
        recipe_create = await recipe_extractor.extract_recipe(
            video.transcript,
            video.title or video.original_filename,
            video.id
        )
        
        if recipe_create:
            # Save recipe
            recipe = recipe_service.create_recipe(recipe_create)
            print(f"✅ Recipe extracted successfully!")
            print(f"   📝 Title: {recipe_create.title}")
            print(f"   🥘 Ingredients: {len(recipe_create.ingredients)}")
            print(f"   📋 Steps: {len(recipe_create.instructions)}")
            print(f"   🎯 Confidence: {recipe_create.extraction_confidence:.2f}")
        else:
            print(f"⏭️  Not detected as a cooking video")
        
    except Exception as e:
        print(f"❌ Recipe extraction failed: {e}")
        raise
    finally:
        db.close()


def list_cooking_videos():
    """List videos that might be cooking videos based on tags"""
    print("🔍 Scanning for potential cooking videos...")
    
    db = SessionLocal()
    
    try:
        # Get videos with cooking-related tags
        cooking_keywords = ['food', 'cooking', 'recipe', 'kitchen', 'cook', 'bake', 'fry', 'comida', 'cocina', 'receta']
        
        videos = db.query(Video).filter(
            Video.transcript.isnot(None),
            Video.transcript != ""
        ).all()
        
        cooking_videos = []
        
        for video in videos:
            # Check if video has cooking-related content
            text_content = f"{video.title or ''} {video.transcript or ''}".lower()
            
            cooking_score = sum(1 for keyword in cooking_keywords if keyword in text_content)
            
            if cooking_score >= 2:  # At least 2 cooking keywords
                cooking_videos.append((video, cooking_score))
        
        # Sort by cooking score
        cooking_videos.sort(key=lambda x: x[1], reverse=True)
        
        print(f"📊 Found {len(cooking_videos)} potential cooking videos:")
        
        for video, score in cooking_videos[:10]:  # Show top 10
            has_recipe = db.query(Recipe).filter(Recipe.video_id == video.id).first() is not None
            status = "✅ Has recipe" if has_recipe else "⏳ No recipe"
            print(f"   🎬 ID {video.id}: {video.title or video.original_filename} (score: {score}) - {status}")
        
        if len(cooking_videos) > 10:
            print(f"   ... and {len(cooking_videos) - 10} more")
        
    except Exception as e:
        print(f"❌ Error listing cooking videos: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Extract recipes from tagTok videos")
    parser.add_argument("--all", action="store_true", help="Extract recipes from all videos")
    parser.add_argument("--video-id", type=int, help="Extract recipe for specific video ID")
    parser.add_argument("--list", action="store_true", help="List potential cooking videos")
    
    args = parser.parse_args()
    
    if args.all:
        asyncio.run(extract_recipes_from_existing_videos())
    elif args.video_id:
        asyncio.run(extract_recipe_for_specific_video(args.video_id))
    elif args.list:
        list_cooking_videos()
    else:
        print("🍳 tagTok Recipe Extraction Tool")
        print("=" * 40)
        print("Usage:")
        print("  --all          Extract recipes from all videos")
        print("  --video-id ID  Extract recipe for specific video")
        print("  --list         List potential cooking videos")
        print("\nExamples:")
        print("  python extract_recipes_batch.py --all")
        print("  python extract_recipes_batch.py --video-id 42")
        print("  python extract_recipes_batch.py --list")
