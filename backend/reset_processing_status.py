#!/usr/bin/env python3
"""
Script to reset videos stuck in processing state
"""
import sys
import os
sys.path.append('.')

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models.database import Video, ProcessingJob

# Database configuration
DATABASE_URL = "sqlite:///db/tagTok.db"

def reset_stuck_processing():
    """Reset videos and jobs stuck in processing state"""
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Find videos stuck in processing
        stuck_videos = db.query(Video).filter(
            Video.processing_status == "processing"
        ).all()
        
        print(f"Found {len(stuck_videos)} videos stuck in processing state")
        
        for video in stuck_videos:
            print(f"Resetting video {video.id}: {video.original_filename}")
            video.processing_status = "pending"
            video.processing_progress = 0
        
        # Find processing jobs stuck in running state
        stuck_jobs = db.query(ProcessingJob).filter(
            ProcessingJob.status == "running"
        ).all()
        
        print(f"Found {len(stuck_jobs)} processing jobs stuck in running state")
        
        for job in stuck_jobs:
            print(f"Resetting job {job.id} for video {job.video_id}")
            job.status = "failed"
            job.error_message = "Reset due to stuck processing state"
        
        # Commit changes
        db.commit()
        print("✅ Successfully reset stuck processing states")
        
        # Show current processing status
        processing_count = db.query(Video).filter(Video.processing_status == "processing").count()
        pending_count = db.query(Video).filter(Video.processing_status == "pending").count()
        completed_count = db.query(Video).filter(Video.processing_status == "completed").count()
        failed_count = db.query(Video).filter(Video.processing_status == "failed").count()
        
        print(f"\nCurrent status:")
        print(f"  Processing: {processing_count}")
        print(f"  Pending: {pending_count}")
        print(f"  Completed: {completed_count}")
        print(f"  Failed: {failed_count}")
        
    except Exception as e:
        print(f"❌ Error resetting processing status: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    reset_stuck_processing()
