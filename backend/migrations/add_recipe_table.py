#!/usr/bin/env python3
"""
Migration script to add Recipe table to existing database
Run this script to add recipe functionality to existing tagTok installations
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add parent directory to path to import models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import engine, Base, Recipe
from sqlalchemy import text


def migrate_database():
    """Add Recipe table to existing database"""
    print("🔄 Starting database migration to add Recipe table...")
    
    try:
        # Check if we're using SQLite
        if "sqlite" in str(engine.url):
            db_path = str(engine.url).replace("sqlite:///", "")
            print(f"📁 Database path: {db_path}")
            
            # Check if database exists
            if not os.path.exists(db_path):
                print("❌ Database file not found. Creating new database...")
                Base.metadata.create_all(bind=engine)
                print("✅ New database created with Recipe table")
                return
            
            # Connect to database and check if Recipe table exists
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check if recipes table exists
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='recipes'
            """)
            
            if cursor.fetchone():
                print("✅ Recipe table already exists, no migration needed")
                conn.close()
                return
            
            print("📝 Recipe table not found, creating...")
            
            # Create the recipes table
            cursor.execute("""
                CREATE TABLE recipes (
                    id INTEGER PRIMARY KEY,
                    video_id INTEGER NOT NULL UNIQUE,
                    title VARCHAR,
                    description TEXT,
                    ingredients JSON,
                    instructions JSON,
                    prep_time VARCHAR,
                    cook_time VARCHAR,
                    total_time VARCHAR,
                    servings VARCHAR,
                    difficulty VARCHAR,
                    cuisine_type VARCHAR,
                    extracted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    extraction_confidence REAL,
                    FOREIGN KEY(video_id) REFERENCES videos (id)
                )
            """)
            
            conn.commit()
            conn.close()
            
            print("✅ Recipe table created successfully")
            
        else:
            # For PostgreSQL or other databases, use SQLAlchemy
            print("🔄 Using SQLAlchemy to create Recipe table...")
            Recipe.__table__.create(bind=engine, checkfirst=True)
            print("✅ Recipe table created successfully")
        
        print("🎉 Database migration completed successfully!")
        print("\n📋 Recipe features now available:")
        print("   • Automatic recipe extraction from cooking videos")
        print("   • Structured ingredient and instruction storage")
        print("   • Recipe search and filtering")
        print("   • API endpoints: /api/recipes/")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        raise


def verify_migration():
    """Verify that the migration was successful"""
    try:
        from sqlalchemy.orm import sessionmaker
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # Try to query the recipes table
        result = session.execute(text("SELECT COUNT(*) FROM recipes")).scalar()
        print(f"✅ Migration verified: Recipe table accessible (current count: {result})")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ Migration verification failed: {e}")
        return False


if __name__ == "__main__":
    print("🚀 tagTok Recipe Migration Tool")
    print("=" * 50)
    
    try:
        migrate_database()
        
        if verify_migration():
            print("\n🎉 Migration completed successfully!")
            print("\nNext steps:")
            print("1. Restart your tagTok backend service")
            print("2. Existing videos will be processed for recipes on next sync")
            print("3. New cooking videos will automatically extract recipes")
            print("4. Access recipe API at: /api/recipes/")
        else:
            print("\n❌ Migration verification failed")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        sys.exit(1)
