#!/bin/bash

# Daily Video Database Sync Script
# This script runs the video sync utility to ensure database and filesystem are in sync

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$PROJECT_DIR/logs"
LOG_FILE="$LOG_DIR/sync-$(date +%Y%m%d).log"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to send notification (can be extended to send emails, Slack, etc.)
notify() {
    local status="$1"
    local message="$2"
    log "NOTIFICATION [$status]: $message"
    
    # You can add notification integrations here:
    # - Email notifications
    # - Slack webhooks
    # - Discord webhooks
    # - etc.
}

# Main sync function
run_sync() {
    log "Starting daily video database synchronization"
    
    # Check if Docker Compose is running
    if ! docker-compose -f "$PROJECT_DIR/docker-compose.yml" ps | grep -q "Up"; then
        log "ERROR: tagTok containers are not running"
        notify "ERROR" "tagTok containers are not running - sync aborted"
        return 1
    fi
    
    # Run the sync script
    log "Running video sync utility..."
    
    if docker-compose -f "$PROJECT_DIR/docker-compose.yml" exec -T backend python utils/sync_videos.py >> "$LOG_FILE" 2>&1; then
        log "Video sync completed successfully"
        
        # Get final counts
        local fs_count=$(docker-compose -f "$PROJECT_DIR/docker-compose.yml" exec -T backend find videos/ -name "*.mp4" | wc -l)
        local db_count=$(curl -s "http://localhost:8000/videos/?limit=1000" | jq 'length' 2>/dev/null || echo "unknown")
        
        log "Final counts: Filesystem=$fs_count, Database=$db_count"
        notify "SUCCESS" "Video sync completed. Filesystem: $fs_count videos, Database: $db_count videos"
        
        return 0
    else
        log "ERROR: Video sync failed"
        notify "ERROR" "Video sync failed - check logs for details"
        return 1
    fi
}

# Cleanup old logs (keep last 30 days)
cleanup_logs() {
    log "Cleaning up old log files..."
    find "$LOG_DIR" -name "sync-*.log" -mtime +30 -delete 2>/dev/null || true
    log "Log cleanup completed"
}

# Health check function
health_check() {
    log "Performing health check..."
    
    # Check if API is responding
    if curl -s -f "http://localhost:8000/health" > /dev/null; then
        log "API health check: OK"
    else
        log "WARNING: API health check failed"
        notify "WARNING" "API health check failed"
    fi
    
    # Check disk space
    local disk_usage=$(df "$PROJECT_DIR/data" | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 90 ]; then
        log "WARNING: Disk usage is at ${disk_usage}%"
        notify "WARNING" "Disk usage is high: ${disk_usage}%"
    else
        log "Disk usage: ${disk_usage}% (OK)"
    fi
}

# Main execution
main() {
    log "=== Daily Sync Job Started ==="
    
    # Change to project directory
    cd "$PROJECT_DIR"
    
    # Run health check
    health_check
    
    # Run sync
    if run_sync; then
        log "Daily sync job completed successfully"
        exit_code=0
    else
        log "Daily sync job failed"
        exit_code=1
    fi
    
    # Cleanup old logs
    cleanup_logs
    
    log "=== Daily Sync Job Finished ==="
    
    exit $exit_code
}

# Handle script arguments
case "${1:-}" in
    --dry-run)
        log "Running in dry-run mode"
        docker-compose -f "$PROJECT_DIR/docker-compose.yml" exec -T backend python utils/sync_videos.py --dry-run
        ;;
    --help)
        echo "Usage: $0 [--dry-run|--help]"
        echo ""
        echo "Daily video database synchronization script for tagTok"
        echo ""
        echo "Options:"
        echo "  --dry-run    Show what would be synced without making changes"
        echo "  --help       Show this help message"
        echo ""
        echo "This script is designed to be run daily via cron to ensure"
        echo "the video database stays in sync with the filesystem."
        ;;
    *)
        main
        ;;
esac
