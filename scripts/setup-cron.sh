#!/bin/bash

# Setup Cron Job for Daily Video Sync
# This script sets up a daily cron job to sync the video database

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
SYNC_SCRIPT="$SCRIPT_DIR/daily-sync.sh"

echo "Setting up daily video sync cron job..."

# Check if the sync script exists
if [ ! -f "$SYNC_SCRIPT" ]; then
    echo "ERROR: Sync script not found at $SYNC_SCRIPT"
    exit 1
fi

# Make sure the sync script is executable
chmod +x "$SYNC_SCRIPT"

# Create cron job entry
CRON_JOB="0 2 * * * $SYNC_SCRIPT >> $PROJECT_DIR/logs/cron.log 2>&1"

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "$SYNC_SCRIPT"; then
    echo "Cron job already exists. Updating..."
    # Remove existing job and add new one
    (crontab -l 2>/dev/null | grep -v "$SYNC_SCRIPT"; echo "$CRON_JOB") | crontab -
else
    echo "Adding new cron job..."
    # Add new job to existing crontab
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
fi

echo "✅ Cron job setup complete!"
echo "📅 The video sync will run daily at 2:00 AM"
echo "📝 Logs will be saved to: $PROJECT_DIR/logs/"
echo ""
echo "To view current cron jobs: crontab -l"
echo "To test the sync manually: $SYNC_SCRIPT --dry-run"
echo "To remove the cron job: crontab -e (then delete the line)"
echo ""
echo "Current cron jobs:"
crontab -l 2>/dev/null || echo "No cron jobs found"
