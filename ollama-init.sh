#!/bin/bash

# Ollama initialization script
# This script pulls the required model when the container starts

echo "Starting Ollama initialization..."

# Wait for Ollama service to be ready
echo "Waiting for Ollama service to start..."
while ! curl -f http://localhost:11434/api/tags >/dev/null 2>&1; do
    echo "Waiting for Ollama to be ready..."
    sleep 5
done

echo "Ollama service is ready!"

# Check if llama3.2:3b model is already available
if ollama list | grep -q "llama3.2:3b"; then
    echo "llama3.2:3b model is already available"
else
    echo "Pulling llama3.2:3b model..."
    ollama pull llama3.2:3b
    echo "Model pulled successfully!"
fi

echo "Ollama initialization completed!"
